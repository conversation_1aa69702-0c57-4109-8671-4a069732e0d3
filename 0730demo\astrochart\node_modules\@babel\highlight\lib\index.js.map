{"version": 3, "names": ["_jsTokens", "require", "_helperValidatorIdentifier", "_chalk", "sometimesKeywords", "Set", "getDefs", "chalk", "keyword", "cyan", "capitalized", "yellow", "jsxIdentifier", "punctuator", "number", "magenta", "string", "green", "regex", "comment", "grey", "invalid", "white", "bgRed", "bold", "NEWLINE", "BRACKET", "tokenize", "JSX_TAG", "getTokenType", "token", "offset", "text", "type", "isKeyword", "value", "isStrictReservedWord", "has", "test", "slice", "toLowerCase", "match", "jsTokens", "default", "exec", "matchToToken", "index", "highlightTokens", "defs", "highlighted", "colorize", "split", "map", "str", "join", "should<PERSON><PERSON><PERSON>", "options", "Chalk", "supportsColor", "forceColor", "getChalk", "constructor", "enabled", "level", "highlight", "code"], "sources": ["../src/index.ts"], "sourcesContent": ["/// <reference path=\"../../../lib/third-party-libs.d.ts\" />\n\nimport type { Token as JSToken, JSXToken } from \"js-tokens\";\nimport jsTokens from \"js-tokens\";\n\nimport {\n  isStrictReservedWord,\n  isKeyword,\n} from \"@babel/helper-validator-identifier\";\nimport Chalk from \"chalk\";\n\ntype ChalkClass = ReturnType<typeof getChalk>;\n\n/**\n * Names that are always allowed as identifiers, but also appear as keywords\n * within certain syntactic productions.\n *\n * https://tc39.es/ecma262/#sec-keywords-and-reserved-words\n *\n * `target` has been omitted since it is very likely going to be a false\n * positive.\n */\nconst sometimesKeywords = new Set([\"as\", \"async\", \"from\", \"get\", \"of\", \"set\"]);\n\ntype InternalTokenType =\n  | \"keyword\"\n  | \"capitalized\"\n  | \"jsxIdentifier\"\n  | \"punctuator\"\n  | \"number\"\n  | \"string\"\n  | \"regex\"\n  | \"comment\"\n  | \"invalid\";\n\ntype Token = {\n  type: InternalTokenType | \"uncolored\";\n  value: string;\n};\n/**\n * Chalk styles for token types.\n */\nfunction getDefs(chalk: ChalkClass): Record<InternalTokenType, ChalkClass> {\n  return {\n    keyword: chalk.cyan,\n    capitalized: chalk.yellow,\n    jsxIdentifier: chalk.yellow,\n    punctuator: chalk.yellow,\n    number: chalk.magenta,\n    string: chalk.green,\n    regex: chalk.magenta,\n    comment: chalk.grey,\n    invalid: chalk.white.bgRed.bold,\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * RegExp to test for the three types of brackets.\n */\nconst BRACKET = /^[()[\\]{}]$/;\n\nlet tokenize: (\n  text: string,\n) => Generator<{ type: InternalTokenType | \"uncolored\"; value: string }>;\n\nif (process.env.BABEL_8_BREAKING) {\n  /**\n   * Get the type of token, specifying punctuator type.\n   */\n  const getTokenType = function (\n    token: JSToken | JSXToken,\n  ): InternalTokenType | \"uncolored\" {\n    if (token.type === \"IdentifierName\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"Punctuator\" && BRACKET.test(token.value)) {\n      return \"uncolored\";\n    }\n\n    if (token.type === \"Invalid\" && token.value === \"@\") {\n      return \"punctuator\";\n    }\n\n    switch (token.type) {\n      case \"NumericLiteral\":\n        return \"number\";\n\n      case \"StringLiteral\":\n      case \"JSXString\":\n      case \"NoSubstitutionTemplate\":\n        return \"string\";\n\n      case \"RegularExpressionLiteral\":\n        return \"regex\";\n\n      case \"Punctuator\":\n      case \"JSXPunctuator\":\n        return \"punctuator\";\n\n      case \"MultiLineComment\":\n      case \"SingleLineComment\":\n        return \"comment\";\n\n      case \"Invalid\":\n      case \"JSXInvalid\":\n        return \"invalid\";\n\n      case \"JSXIdentifier\":\n        return \"jsxIdentifier\";\n\n      default:\n        return \"uncolored\";\n    }\n  };\n\n  /**\n   * Turn a string of JS into an array of objects.\n   */\n  tokenize = function* (text: string): Generator<Token> {\n    for (const token of jsTokens(text, { jsx: true })) {\n      switch (token.type) {\n        case \"TemplateHead\":\n          yield { type: \"string\", value: token.value.slice(0, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateMiddle\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1, -2) };\n          yield { type: \"punctuator\", value: \"${\" };\n          break;\n\n        case \"TemplateTail\":\n          yield { type: \"punctuator\", value: \"}\" };\n          yield { type: \"string\", value: token.value.slice(1) };\n          break;\n\n        default:\n          yield {\n            type: getTokenType(token),\n            value: token.value,\n          };\n      }\n    }\n  };\n} else {\n  /**\n   * RegExp to test for what seems to be a JSX tag name.\n   */\n  const JSX_TAG = /^[a-z][\\w-]*$/i;\n\n  // The token here is defined in js-tokens@4. However we don't bother\n  // typing it since the whole block will be removed in Babel 8\n  const getTokenType = function (token: any, offset: number, text: string) {\n    if (token.type === \"name\") {\n      if (\n        isKeyword(token.value) ||\n        isStrictReservedWord(token.value, true) ||\n        sometimesKeywords.has(token.value)\n      ) {\n        return \"keyword\";\n      }\n\n      if (\n        JSX_TAG.test(token.value) &&\n        (text[offset - 1] === \"<\" || text.slice(offset - 2, offset) == \"</\")\n      ) {\n        return \"jsxIdentifier\";\n      }\n\n      if (token.value[0] !== token.value[0].toLowerCase()) {\n        return \"capitalized\";\n      }\n    }\n\n    if (token.type === \"punctuator\" && BRACKET.test(token.value)) {\n      return \"bracket\";\n    }\n\n    if (\n      token.type === \"invalid\" &&\n      (token.value === \"@\" || token.value === \"#\")\n    ) {\n      return \"punctuator\";\n    }\n\n    return token.type;\n  };\n\n  tokenize = function* (text: string) {\n    let match;\n    while ((match = (jsTokens as any).default.exec(text))) {\n      const token = (jsTokens as any).matchToToken(match);\n\n      yield {\n        type: getTokenType(token, match.index, text),\n        value: token.value,\n      };\n    }\n  };\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\nfunction highlightTokens(defs: Record<string, ChalkClass>, text: string) {\n  let highlighted = \"\";\n\n  for (const { type, value } of tokenize(text)) {\n    const colorize = defs[type];\n    if (colorize) {\n      highlighted += value\n        .split(NEWLINE)\n        .map(str => colorize(str))\n        .join(\"\\n\");\n    } else {\n      highlighted += value;\n    }\n  }\n\n  return highlighted;\n}\n\n/**\n * Highlight `text` using the token definitions in `defs`.\n */\n\ntype Options = {\n  forceColor?: boolean;\n};\n\n/**\n * Whether the code should be highlighted given the passed options.\n */\nexport function shouldHighlight(options: Options): boolean {\n  return !!Chalk.supportsColor || options.forceColor;\n}\n\n/**\n * The Chalk instance that should be used given the passed options.\n */\nexport function getChalk(options: Options) {\n  return options.forceColor\n    ? new Chalk.constructor({ enabled: true, level: 1 })\n    : Chalk;\n}\n\n/**\n * Highlight `code`.\n */\nexport default function highlight(code: string, options: Options = {}): string {\n  if (code !== \"\" && shouldHighlight(options)) {\n    const chalk = getChalk(options);\n    const defs = getDefs(chalk);\n    return highlightTokens(defs, code);\n  } else {\n    return code;\n  }\n}\n"], "mappings": ";;;;;;;;AAGA,IAAAA,SAAA,GAAAC,OAAA;AAEA,IAAAC,0BAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAF,OAAA;AAaA,MAAMG,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAoB9E,SAASC,OAAOA,CAACC,KAAiB,EAAyC;EACzE,OAAO;IACLC,OAAO,EAAED,KAAK,CAACE,IAAI;IACnBC,WAAW,EAAEH,KAAK,CAACI,MAAM;IACzBC,aAAa,EAAEL,KAAK,CAACI,MAAM;IAC3BE,UAAU,EAAEN,KAAK,CAACI,MAAM;IACxBG,MAAM,EAAEP,KAAK,CAACQ,OAAO;IACrBC,MAAM,EAAET,KAAK,CAACU,KAAK;IACnBC,KAAK,EAAEX,KAAK,CAACQ,OAAO;IACpBI,OAAO,EAAEZ,KAAK,CAACa,IAAI;IACnBC,OAAO,EAAEd,KAAK,CAACe,KAAK,CAACC,KAAK,CAACC;EAC7B,CAAC;AACH;AAKA,MAAMC,OAAO,GAAG,yBAAyB;AAKzC,MAAMC,OAAO,GAAG,aAAa;AAE7B,IAAIC,QAEoE;AA6FjE;EAIL,MAAMC,OAAO,GAAG,gBAAgB;EAIhC,MAAMC,YAAY,GAAG,SAAAA,CAAUC,KAAU,EAAEC,MAAc,EAAEC,IAAY,EAAE;IACvE,IAAIF,KAAK,CAACG,IAAI,KAAK,MAAM,EAAE;MACzB,IACE,IAAAC,oCAAS,EAACJ,KAAK,CAACK,KAAK,CAAC,IACtB,IAAAC,+CAAoB,EAACN,KAAK,CAACK,KAAK,EAAE,IAAI,CAAC,IACvC/B,iBAAiB,CAACiC,GAAG,CAACP,KAAK,CAACK,KAAK,CAAC,EAClC;QACA,OAAO,SAAS;MAClB;MAEA,IACEP,OAAO,CAACU,IAAI,CAACR,KAAK,CAACK,KAAK,CAAC,KACxBH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIC,IAAI,CAACO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEA,MAAM,CAAC,IAAI,IAAI,CAAC,EACpE;QACA,OAAO,eAAe;MACxB;MAEA,IAAID,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,KAAKL,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,EAAE;QACnD,OAAO,aAAa;MACtB;IACF;IAEA,IAAIV,KAAK,CAACG,IAAI,KAAK,YAAY,IAAIP,OAAO,CAACY,IAAI,CAACR,KAAK,CAACK,KAAK,CAAC,EAAE;MAC5D,OAAO,SAAS;IAClB;IAEA,IACEL,KAAK,CAACG,IAAI,KAAK,SAAS,KACvBH,KAAK,CAACK,KAAK,KAAK,GAAG,IAAIL,KAAK,CAACK,KAAK,KAAK,GAAG,CAAC,EAC5C;MACA,OAAO,YAAY;IACrB;IAEA,OAAOL,KAAK,CAACG,IAAI;EACnB,CAAC;EAEDN,QAAQ,GAAG,UAAAA,CAAWK,IAAY,EAAE;IAClC,IAAIS,KAAK;IACT,OAAQA,KAAK,GAAIC,SAAQ,CAASC,OAAO,CAACC,IAAI,CAACZ,IAAI,CAAC,EAAG;MACrD,MAAMF,KAAK,GAAIY,SAAQ,CAASG,YAAY,CAACJ,KAAK,CAAC;MAEnD,MAAM;QACJR,IAAI,EAAEJ,YAAY,CAACC,KAAK,EAAEW,KAAK,CAACK,KAAK,EAAEd,IAAI,CAAC;QAC5CG,KAAK,EAAEL,KAAK,CAACK;MACf,CAAC;IACH;EACF,CAAC;AACH;AAKA,SAASY,eAAeA,CAACC,IAAgC,EAAEhB,IAAY,EAAE;EACvE,IAAIiB,WAAW,GAAG,EAAE;EAEpB,KAAK,MAAM;IAAEhB,IAAI;IAAEE;EAAM,CAAC,IAAIR,QAAQ,CAACK,IAAI,CAAC,EAAE;IAC5C,MAAMkB,QAAQ,GAAGF,IAAI,CAACf,IAAI,CAAC;IAC3B,IAAIiB,QAAQ,EAAE;MACZD,WAAW,IAAId,KAAK,CACjBgB,KAAK,CAAC1B,OAAO,CAAC,CACd2B,GAAG,CAACC,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CACzBC,IAAI,CAAC,IAAI,CAAC;IACf,CAAC,MAAM;MACLL,WAAW,IAAId,KAAK;IACtB;EACF;EAEA,OAAOc,WAAW;AACpB;AAaO,SAASM,eAAeA,CAACC,OAAgB,EAAW;EACzD,OAAO,CAAC,CAACC,MAAK,CAACC,aAAa,IAAIF,OAAO,CAACG,UAAU;AACpD;AAKO,SAASC,QAAQA,CAACJ,OAAgB,EAAE;EACzC,OAAOA,OAAO,CAACG,UAAU,GACrB,IAAIF,MAAK,CAACI,WAAW,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC,GAClDN,MAAK;AACX;AAKe,SAASO,SAASA,CAACC,IAAY,EAAET,OAAgB,GAAG,CAAC,CAAC,EAAU;EAC7E,IAAIS,IAAI,KAAK,EAAE,IAAIV,eAAe,CAACC,OAAO,CAAC,EAAE;IAC3C,MAAMjD,KAAK,GAAGqD,QAAQ,CAACJ,OAAO,CAAC;IAC/B,MAAMR,IAAI,GAAG1C,OAAO,CAACC,KAAK,CAAC;IAC3B,OAAOwC,eAAe,CAACC,IAAI,EAAEiB,IAAI,CAAC;EACpC,CAAC,MAAM;IACL,OAAOA,IAAI;EACb;AACF"}