{"version": 3, "names": ["ParseErrorCode", "SyntaxError", "SourceTypeModuleError", "exports", "reflect", "keys", "last", "length", "get", "reduce", "object", "key", "set", "value", "item", "i", "instantiate", "constructor", "properties", "descriptors", "Object", "map", "filter", "descriptor", "enumerable", "assign", "split", "instance", "defineProperty", "configurable"], "sources": ["../../src/parse-error/credentials.ts"], "sourcesContent": ["export const enum ParseErrorCode {\n  SyntaxError = \"BABEL_PARSER_SYNTAX_ERROR\",\n  SourceTypeModuleError = \"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\",\n}\n\nexport type SyntaxPlugin =\n  | \"flow\"\n  | \"typescript\"\n  | \"jsx\"\n  | \"pipelineOperator\"\n  | \"placeholders\";\n\nexport type ToMessage<ErrorDetails> = (self: ErrorDetails) => string;\n\nexport type ParseErrorCredentials<ErrorDetails> = {\n  code: ParseErrorCode;\n  reasonCode: string;\n  syntaxPlugin?: SyntaxPlugin;\n  toMessage: ToMessage<ErrorDetails>;\n};\n\nconst reflect = (keys: string[], last = keys.length - 1) => ({\n  get(this: unknown): unknown {\n    return keys.reduce(\n      (object, key) =>\n        // @ts-expect-error key should index object\n        object[key],\n      this,\n    );\n  },\n  set(this: unknown, value: unknown) {\n    keys.reduce(\n      // @ts-expect-error key should index item\n      (item, key, i) => (i === last ? (item[key] = value) : item[key]),\n      this,\n    );\n  },\n});\n\nconst instantiate = <T>(\n  constructor: new () => T,\n  properties: any,\n  descriptors: any,\n) =>\n  Object.keys(descriptors)\n    .map(key => [key, descriptors[key]])\n    .filter(([, descriptor]) => !!descriptor)\n    .map(([key, descriptor]) => [\n      key,\n      typeof descriptor === \"function\"\n        ? { value: descriptor, enumerable: false }\n        : typeof descriptor.reflect === \"string\"\n        ? { ...descriptor, ...reflect(descriptor.reflect.split(\".\")) }\n        : descriptor,\n    ])\n    .reduce(\n      (instance, [key, descriptor]) =>\n        Object.defineProperty(instance, key, {\n          configurable: true,\n          ...descriptor,\n        }),\n      Object.assign(new constructor(), properties),\n    );\n\nexport { instantiate };\n"], "mappings": ";;;;;;IAAkBA,cAAc;EAAAC,WAAA;EAAAC,qBAAA;AAAA;AAAAC,OAAA,CAAAH,cAAA,GAAAA,cAAA;AAqBhC,MAAMI,OAAO,GAAGA,CAACC,IAAc,EAAEC,IAAI,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,MAAM;EAC3DC,GAAGA,CAAA,EAAyB;IAC1B,OAAOH,IAAI,CAACI,MAAM,CAChB,CAACC,MAAM,EAAEC,GAAG,KAEVD,MAAM,CAACC,GAAG,CAAC,EACb,IACF,CAAC;EACH,CAAC;EACDC,GAAGA,CAAgBC,KAAc,EAAE;IACjCR,IAAI,CAACI,MAAM,CAET,CAACK,IAAI,EAAEH,GAAG,EAAEI,CAAC,KAAMA,CAAC,KAAKT,IAAI,GAAIQ,IAAI,CAACH,GAAG,CAAC,GAAGE,KAAK,GAAIC,IAAI,CAACH,GAAG,CAAE,EAChE,IACF,CAAC;EACH;AACF,CAAC,CAAC;AAEF,MAAMK,WAAW,GAAGA,CAClBC,WAAwB,EACxBC,UAAe,EACfC,WAAgB,KAEhBC,MAAM,CAACf,IAAI,CAACc,WAAW,CAAC,CACrBE,GAAG,CAACV,GAAG,IAAI,CAACA,GAAG,EAAEQ,WAAW,CAACR,GAAG,CAAC,CAAC,CAAC,CACnCW,MAAM,CAAC,CAAC,GAAGC,UAAU,CAAC,KAAK,CAAC,CAACA,UAAU,CAAC,CACxCF,GAAG,CAAC,CAAC,CAACV,GAAG,EAAEY,UAAU,CAAC,KAAK,CAC1BZ,GAAG,EACH,OAAOY,UAAU,KAAK,UAAU,GAC5B;EAAEV,KAAK,EAAEU,UAAU;EAAEC,UAAU,EAAE;AAAM,CAAC,GACxC,OAAOD,UAAU,CAACnB,OAAO,KAAK,QAAQ,GAAAgB,MAAA,CAAAK,MAAA,KACjCF,UAAU,EAAKnB,OAAO,CAACmB,UAAU,CAACnB,OAAO,CAACsB,KAAK,CAAC,GAAG,CAAC,CAAC,IAC1DH,UAAU,CACf,CAAC,CACDd,MAAM,CACL,CAACkB,QAAQ,EAAE,CAAChB,GAAG,EAAEY,UAAU,CAAC,KAC1BH,MAAM,CAACQ,cAAc,CAACD,QAAQ,EAAEhB,GAAG,EAAAS,MAAA,CAAAK,MAAA;EACjCI,YAAY,EAAE;AAAI,GACfN,UAAU,CACd,CAAC,EACJH,MAAM,CAACK,MAAM,CAAC,IAAIR,WAAW,CAAC,CAAC,EAAEC,UAAU,CAC7C,CAAC;AAACf,OAAA,CAAAa,WAAA,GAAAA,WAAA"}