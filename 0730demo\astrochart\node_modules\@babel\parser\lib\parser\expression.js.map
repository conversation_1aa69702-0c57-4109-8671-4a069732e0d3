{"version": 3, "names": ["_types", "require", "_lval", "_identifier", "_location", "_scopeflags", "_util", "_productionParameter", "_expressionScope", "_parseError", "_pipelineOperatorErrors", "_comments", "_node", "ExpressionParser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkProto", "prop", "isRecord", "protoRef", "refExpressionErrors", "type", "isObjectMethod", "computed", "shorthand", "key", "name", "value", "raise", "Errors", "RecordNoProto", "at", "used", "doubleProtoLoc", "loc", "start", "DuplicateProto", "shouldExitDescending", "expr", "potentialArrowAt", "getExpression", "enterInitialScopes", "nextToken", "parseExpression", "match", "unexpected", "finalizeRemainingComments", "comments", "state", "errors", "options", "tokens", "disallowIn", "disallowInAnd", "parseExpressionBase", "allowInAnd", "startLoc", "parseMaybeAssign", "node", "startNodeAt", "expressions", "eat", "push", "toReferencedList", "finishNode", "parseMaybeAssignDisallowIn", "afterLeftParse", "parseMaybeAssignAllowIn", "setOptionalParametersError", "resultError", "_resultError$loc", "optionalParametersLoc", "isContextual", "prodParam", "<PERSON><PERSON><PERSON>", "left", "parseYield", "call", "ownExpressionErrors", "ExpressionErrors", "tokenIsIdentifier", "parseMaybeConditional", "tokenIsAssignment", "operator", "toAssignable", "startIndex", "index", "shorthandAssignLoc", "privateKeyLoc", "checkDestructuringPrivate", "next", "right", "checkLVal", "in", "checkExpressionErrors", "parseExprOps", "parseConditional", "test", "consequent", "expect", "alternate", "parseMaybeUnaryOrPrivate", "parsePrivateName", "parseMaybeUnary", "parseExprOp", "leftStartLoc", "minPrec", "isPrivateName", "getPrivateNameSV", "tokenOperatorPrecedence", "hasIn", "PrivateInExpectedIn", "identifierName", "classScope", "usePrivateName", "op", "tokenIsOperator", "prec", "expectPlugin", "inFSharpPipelineDirectBody", "checkPipelineAtInfixOperator", "logical", "coalesce", "hasPlugin", "proposal", "hasAwait", "UnexpectedAwaitAfterPipelineBody", "parseExprOpRightExpr", "finishedNode", "nextOp", "MixingCoalesceWithLogical", "getPluginOption", "withTopicBindingContext", "parseHackPipeBody", "PipeBodyIsTighter", "parseSmartPipelineBodyInStyle", "parseExprOpBaseRightExpr", "withSoloAwaitPermittingContext", "parseFSharpPipelineBody", "tokenIsRightAssociative", "_body$extra", "body", "requiredParentheses", "UnparenthesizedPipeBodyDescriptions", "has", "extra", "parenthesized", "PipeUnparenthesizedBody", "topicReferenceWasUsedInCurrentContext", "PipeTopicUnused", "checkExponentialAfterUnary", "UnexpectedTokenUnaryExponentiation", "argument", "sawUnary", "isAwait", "isAwaitAllowed", "parseAwait", "update", "startNode", "tokenIsPrefix", "prefix", "isDelete", "strict", "arg", "StrictDelete", "hasPropertyAsPrivateName", "DeletePrivateField", "parseUpdate", "startsExpr", "tokenCanStartExpression", "isAmbiguousAwait", "raiseOverwrite", "AwaitNotInAsyncContext", "updateExpressionNode", "parseExprSubscripts", "tokenIsPostfix", "canInsertSemicolon", "parseExprAtom", "parseSubscripts", "base", "noCalls", "optionalChainMember", "maybeAsyncArrow", "atPossibleAsyncArrow", "stop", "parseSubscript", "parseBind", "tokenIsTemplate", "parseTaggedTemplateExpression", "optional", "OptionalChainingNoNew", "lookaheadCharCode", "parseCoverCallAndAsyncArrowHead", "parseMember", "object", "property", "SuperPrivateField", "parseIdentifier", "callee", "parseNoCallExpr", "oldMaybeInArrowParameters", "maybeInArrowParameters", "expressionScope", "enter", "newAsyncArrowScope", "arguments", "parseCallExpressionArguments", "finishCallExpression", "shouldParseAsyncArrow", "validateAsPattern", "exit", "parseAsyncArrowFromCallExpression", "toReferencedArguments", "isParenthesizedExpr", "toReferencedListDeep", "tag", "quasi", "parseTemplate", "OptionalChainingNoTemplate", "lastTokEndLoc", "end", "expectImportAttributesPlugin", "length", "ImportCallArity", "maxArgumentCount", "ImportCallSpreadArgument", "close", "dynamicImport", "allowPlaceholder", "nodeForExtra", "elts", "first", "oldInFSharpPipelineDirectBody", "ImportCallArgumentTrailingComma", "lastTokStartLoc", "addTrailingCommaExtraToNode", "parseExprListItem", "_call$extra", "resetPreviousNodeTrailingComments", "parseArrowExpression", "trailingCommaLoc", "innerComments", "setInnerComments", "trailingComments", "decorators", "parse<PERSON><PERSON><PERSON>", "parseImportMetaProperty", "UnsupportedImport", "parseDo", "readRegexp", "parseRegExpLiteral", "parseNumericLiteral", "parseBigIntLiteral", "parseDecimalLiteral", "parseStringLiteral", "parse<PERSON>ull<PERSON><PERSON><PERSON>", "parseBooleanLiteral", "canBeArrow", "parseParenAndDistinguishExpression", "parseArrayLike", "parseObjectLike", "parseFunctionOrFunctionSent", "parseDecorators", "parseClass", "maybeTakeDecorators", "parseNewOrNewTarget", "UnsupportedBind", "parseTopicReferenceThenEqualsSign", "parseTopicReference", "pipeProposal", "lookaheadCh", "input", "codePointAt", "nextTokenStart", "isIdentifierStart", "expectOnePlugin", "lookaheadInLineCharCode", "parseModuleExpression", "containsEsc", "id", "parseAsyncFunctionExpression", "startNodeAtNode", "parseAsyncArrowUnaryFunction", "topicTokenType", "topicTokenValue", "pos", "endLoc", "createPositionWithColumnOffset", "tokenType", "finishTopicReference", "testTopicReferenceConfiguration", "nodeType", "topicReferenceIsAllowedInCurrentContext", "PrimaryTopicNotAllowed", "PipeTopicUnbound", "registerTopicReference", "PipeTopicUnconfiguredToken", "token", "tokenLabelName", "topicToken", "PipeTopicRequiresHackPipes", "functionFlags", "params", "hasPrecedingLineBreak", "LineTerminatorBeforeArrow", "curPosition", "isAsync", "async", "<PERSON><PERSON><PERSON><PERSON>", "labels", "PARAM_AWAIT", "parseBlock", "scope", "allowDirectSuper", "allowSuperOutsideMethod", "SuperNotAllowed", "allowSuper", "UnexpectedSuper", "UnsupportedSuper", "createIdentifier", "meta", "parseMetaProperty", "parseFunction", "propertyName", "UnsupportedMetaProperty", "target", "onlyValidPropertyName", "inModule", "ImportMetaOutsideModule", "sawUnambiguousESM", "parseLiteralAtNode", "addExtra", "slice", "parseLiteral", "pattern", "flags", "val", "newArrowHeadScope", "innerStartLoc", "exprList", "spreadStartLoc", "optionalCommaStartLoc", "spreadNodeStartLoc", "parseParenItem", "parseRestBinding", "checkCommaAfterRest", "innerEndLoc", "arrowNode", "shouldParseArrow", "parseArrow", "resetEndLocation", "wrapParenthesis", "expression", "createParenthesizedExpressions", "takeSurroundingComments", "parenExpression", "metaProp", "inNonArrowFunction", "inClass", "allowNewTargetOutsideFunction", "UnexpectedNewTarget", "parseNew", "parseNewCallee", "args", "parseExprList", "ImportCallNotNewExpression", "parseTemplateElement", "isTagged", "elemStart", "elem", "InvalidEscapeSequenceTemplate", "firstInvalidTemplateEscapePos", "isTail", "endOffset", "elemEnd", "raw", "replace", "cooked", "tail", "curElt", "quasis", "parseTemplateSubstitution", "readTemplateContinuation", "isPattern", "propHash", "Object", "create", "properties", "parseBindingProperty", "parsePropertyDefinition", "isObjectProperty", "InvalidRecordProperty", "lastTokStart", "maybeAsyncOrAccessorProp", "isLiteralPropertyName", "UnsupportedPropertyDecorator", "parseDecorator", "isAccessor", "parseSpread", "method", "isGenerator", "parsePropertyNamePrefixOperator", "parsePropertyName", "keyName", "kind", "AccessorIsGenerator", "parseObjPropValue", "getGetterSetterExpectedParamCount", "getObjectOrClassMethodParams", "checkGetterSetterParams", "_params", "paramCount", "BadGetterArity", "BadSetterArity", "BadSetterRestParameter", "parseObjectMethod", "finishedProp", "parseMethod", "parseObjectProperty", "parseMaybeDefault", "checkReservedWord", "cloneIdentifier", "InvalidCoverInitializedName", "tokenIsKeywordOrIdentifier", "UnexpectedPrivateField", "initFunction", "generator", "isConstructor", "inClassScope", "SCOPE_FUNCTION", "SCOPE_SUPER", "SCOPE_CLASS", "SCOPE_DIRECT_SUPER", "parseFunctionParams", "parseFunctionBodyAndFinish", "canBePattern", "isTuple", "elements", "SCOPE_ARROW", "PARAM_IN", "setArrowFunctionParameters", "parseFunctionBody", "toAssignableList", "isMethod", "allowExpression", "isExpression", "newExpressionScope", "checkParams", "oldStrict", "currentFlags", "PARAM_RETURN", "hasStrictModeDirective", "nonSimple", "isSimpleParamList", "IllegalLanguageModeDirective", "strictModeChanged", "checkIdentifier", "BIND_OUTSIDE", "isSimpleParameter", "i", "len", "allowDuplicates", "isArrowFunction", "checkClashes", "Set", "formalParameters", "param", "binding", "BIND_VAR", "allowEmpty", "elt", "UnexpectedToken", "UnexpectedArgumentPlaceholder", "liberal", "parseIdentifierName", "tokenIsKeyword", "tokenKeywordOrIdentifierIsKeyword", "replaceToken", "word", "checkKeywords", "isBinding", "canBeReservedWord", "isKeyword", "UnexpectedKeyword", "keyword", "reservedTest", "isReservedWord", "isStrictBindReservedWord", "isStrictReservedWord", "UnexpectedReservedWord", "reservedWord", "YieldBindingIdentifier", "AwaitBindingIdentifier", "inStaticBlock", "AwaitBindingIdentifierInStaticBlock", "recordAsyncArrowParametersError", "inClassAndNotInNonArrowFunction", "ArgumentsInClass", "allowAwaitOutsideFunction", "inFunction", "recordParameterInitializerError", "AwaitExpressionFormalParameter", "ObsoleteAwaitStar", "ambiguousScriptDifferentAst", "soloA<PERSON>t", "YieldInParameter", "delegating", "delegate", "PipelineHeadSequenceExpression", "childExpr", "isSimpleReference", "bodyNode", "checkSmartPipeTopicBodyEarlyErrors", "PipelineBodyNoArrow", "PipelineTopicUnused", "callback", "outerContextTopicState", "topicContext", "maxNumOfResolvableTopics", "maxTopicIndex", "withSmartMixTopicForbiddingContext", "outerContextSoloAwaitState", "prodParamToSet", "prodParamToClear", "ret", "program", "revertScopes", "initializeScopes", "parseProgram", "exports", "default"], "sources": ["../../src/parser/expression.ts"], "sourcesContent": ["// A recursive descent parser operates by defining functions for all\n// syntactic elements, and recursively calling those, each function\n// advancing the input stream and returning an AST node. Precedence\n// of constructs (for example, the fact that `!x[1]` means `!(x[1])`\n// instead of `(!x)[1]` is handled by the fact that the parser\n// function that parses unary prefix operators is called first, and\n// in turn calls the function that parses `[]` subscripts — that\n// way, it'll receive the node for `x[1]` already parsed, and wraps\n// *that* in the unary operator node.\n//\n// Acorn uses an [operator precedence parser][opp] to handle binary\n// operator precedence, because it is much more compact than using\n// the technique outlined above, which uses different, nesting\n// functions to specify precedence, for all of the ten binary\n// precedence levels that JavaScript defines.\n//\n// [opp]: http://en.wikipedia.org/wiki/Operator-precedence_parser\n\nimport {\n  tokenCanStartExpression,\n  tokenIsAssignment,\n  tokenIsIdentifier,\n  tokenIsKeywordOrIdentifier,\n  tokenIsOperator,\n  tokenIsPostfix,\n  tokenIsPrefix,\n  tokenIsRightAssociative,\n  tokenIsTemplate,\n  tokenKeywordOrIdentifierIsKeyword,\n  tokenLabelName,\n  tokenOperatorPrecedence,\n  tt,\n  type TokenType,\n} from \"../tokenizer/types\";\nimport type * as N from \"../types\";\nimport LValParser from \"./lval\";\nimport {\n  isKeyword,\n  isReservedWord,\n  isStrictReservedWord,\n  isStrictBindReservedWord,\n  isIdentifierStart,\n  canBeReservedWord,\n} from \"../util/identifier\";\nimport {\n  type Position,\n  createPositionWithColumnOffset,\n} from \"../util/location\";\nimport * as charCodes from \"charcodes\";\nimport {\n  BIND_OUTSIDE,\n  BIND_VAR,\n  SCOPE_ARROW,\n  SCOPE_CLASS,\n  SCOPE_DIRECT_SUPER,\n  SCOPE_FUNCTION,\n  SCOPE_SUPER,\n} from \"../util/scopeflags\";\nimport { ExpressionErrors } from \"./util\";\nimport {\n  PARAM_AWAIT,\n  PARAM_IN,\n  PARAM_RETURN,\n  functionFlags,\n} from \"../util/production-parameter\";\nimport {\n  newArrowHeadScope,\n  newAsyncArrowScope,\n  newExpressionScope,\n} from \"../util/expression-scope\";\nimport { Errors, type ParseError } from \"../parse-error\";\nimport { UnparenthesizedPipeBodyDescriptions } from \"../parse-error/pipeline-operator-errors\";\nimport { setInnerComments } from \"./comments\";\nimport { cloneIdentifier, type Undone } from \"./node\";\nimport type Parser from \".\";\n\nimport type { SourceType } from \"../options\";\n\nexport default abstract class ExpressionParser extends LValParser {\n  // Forward-declaration: defined in statement.js\n  abstract parseBlock(\n    allowDirectives?: boolean,\n    createNewLexicalScope?: boolean,\n    afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n  ): N.BlockStatement;\n  abstract parseClass(\n    node: N.Class,\n    isStatement: boolean,\n    optionalId?: boolean,\n  ): N.Class;\n  abstract parseDecorators(allowExport?: boolean): void;\n  abstract parseFunction<T extends N.NormalFunction>(\n    node: T,\n    statement?: number,\n    allowExpressionBody?: boolean,\n    isAsync?: boolean,\n  ): T;\n  abstract parseFunctionParams(node: N.Function, isConstructor?: boolean): void;\n  abstract parseBlockOrModuleBlockBody(\n    body: N.Statement[],\n    directives: N.Directive[] | null | undefined,\n    topLevel: boolean,\n    end: TokenType,\n    afterBlockParse?: (hasStrictModeDirective: boolean) => void,\n  ): void;\n  abstract parseProgram(\n    program: N.Program,\n    end: TokenType,\n    sourceType?: SourceType,\n  ): N.Program;\n\n  // For object literal, check if property __proto__ has been used more than once.\n  // If the expression is a destructuring assignment, then __proto__ may appear\n  // multiple times. Otherwise, __proto__ is a duplicated key.\n\n  // For record expression, check if property __proto__ exists\n\n  checkProto(\n    prop: N.ObjectMember | N.SpreadElement,\n    isRecord: boolean | undefined | null,\n    protoRef: {\n      used: boolean;\n    },\n    refExpressionErrors?: ExpressionErrors | null,\n  ): void {\n    if (\n      prop.type === \"SpreadElement\" ||\n      this.isObjectMethod(prop) ||\n      prop.computed ||\n      // @ts-expect-error prop must be an ObjectProperty\n      prop.shorthand\n    ) {\n      return;\n    }\n\n    const key = prop.key;\n    // It is either an Identifier or a String/NumericLiteral\n    const name = key.type === \"Identifier\" ? key.name : key.value;\n\n    if (name === \"__proto__\") {\n      if (isRecord) {\n        this.raise(Errors.RecordNoProto, { at: key });\n        return;\n      }\n      if (protoRef.used) {\n        if (refExpressionErrors) {\n          // Store the first redefinition's position, otherwise ignore because\n          // we are parsing ambiguous pattern\n          if (refExpressionErrors.doubleProtoLoc === null) {\n            refExpressionErrors.doubleProtoLoc = key.loc.start;\n          }\n        } else {\n          this.raise(Errors.DuplicateProto, { at: key });\n        }\n      }\n\n      protoRef.used = true;\n    }\n  }\n\n  shouldExitDescending(expr: N.Expression, potentialArrowAt: number): boolean {\n    return (\n      expr.type === \"ArrowFunctionExpression\" && expr.start === potentialArrowAt\n    );\n  }\n\n  // Convenience method to parse an Expression only\n  getExpression(this: Parser): N.Expression & N.ParserOutput {\n    this.enterInitialScopes();\n    this.nextToken();\n    const expr = this.parseExpression();\n    if (!this.match(tt.eof)) {\n      this.unexpected();\n    }\n    // Unlike parseTopLevel, we need to drain remaining commentStacks\n    // because the top level node is _not_ Program.\n    this.finalizeRemainingComments();\n    expr.comments = this.state.comments;\n    expr.errors = this.state.errors;\n    if (this.options.tokens) {\n      expr.tokens = this.tokens;\n    }\n    // @ts-expect-error fixme: refine types\n    return expr;\n  }\n\n  // ### Expression parsing\n\n  // These nest, from the most general expression type at the top to\n  // 'atomic', nondivisible expression types at the bottom. Most of\n  // the functions will simply let the function (s) below them parse,\n  // and, *if* the syntactic construct they handle is present, wrap\n  // the AST node that the inner parser gave them in another node.\n\n  // Parse a full expression.\n  // - `disallowIn`\n  //   is used to forbid the `in` operator (in for loops initialization expressions)\n  //   When `disallowIn` is true, the production parameter [In] is not present.\n\n  // - `refExpressionErrors `\n  //   provides reference for storing '=' operator inside shorthand\n  //   property assignment in contexts where both object expression\n  //   and object pattern might appear (so it's possible to raise\n  //   delayed syntax error at correct position).\n\n  parseExpression(\n    this: Parser,\n    disallowIn?: boolean,\n    refExpressionErrors?: ExpressionErrors,\n  ): N.Expression {\n    if (disallowIn) {\n      return this.disallowInAnd(() =>\n        this.parseExpressionBase(refExpressionErrors),\n      );\n    }\n    return this.allowInAnd(() => this.parseExpressionBase(refExpressionErrors));\n  }\n\n  // https://tc39.es/ecma262/#prod-Expression\n  parseExpressionBase(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    const expr = this.parseMaybeAssign(refExpressionErrors);\n    if (this.match(tt.comma)) {\n      const node = this.startNodeAt(startLoc);\n      node.expressions = [expr];\n      while (this.eat(tt.comma)) {\n        node.expressions.push(this.parseMaybeAssign(refExpressionErrors));\n      }\n      this.toReferencedList(node.expressions);\n      return this.finishNode(node, \"SequenceExpression\");\n    }\n    return expr;\n  }\n\n  // Set [~In] parameter for assignment expression\n  parseMaybeAssignDisallowIn(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n    afterLeftParse?: Function,\n  ) {\n    return this.disallowInAnd(() =>\n      this.parseMaybeAssign(refExpressionErrors, afterLeftParse),\n    );\n  }\n\n  // Set [+In] parameter for assignment expression\n  parseMaybeAssignAllowIn(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n    afterLeftParse?: Function,\n  ) {\n    return this.allowInAnd(() =>\n      this.parseMaybeAssign(refExpressionErrors, afterLeftParse),\n    );\n  }\n\n  // This method is only used by\n  // the typescript and flow plugins.\n  setOptionalParametersError(\n    refExpressionErrors: ExpressionErrors,\n    resultError?: ParseError<any>,\n  ) {\n    refExpressionErrors.optionalParametersLoc =\n      resultError?.loc ?? this.state.startLoc;\n  }\n\n  // Parse an assignment expression. This includes applications of\n  // operators like `+=`.\n  // https://tc39.es/ecma262/#prod-AssignmentExpression\n  parseMaybeAssign(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n    afterLeftParse?: Function,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    if (this.isContextual(tt._yield)) {\n      if (this.prodParam.hasYield) {\n        let left = this.parseYield();\n        if (afterLeftParse) {\n          left = afterLeftParse.call(this, left, startLoc);\n        }\n        return left;\n      }\n    }\n\n    let ownExpressionErrors;\n    if (refExpressionErrors) {\n      ownExpressionErrors = false;\n    } else {\n      refExpressionErrors = new ExpressionErrors();\n      ownExpressionErrors = true;\n    }\n    const { type } = this.state;\n\n    if (type === tt.parenL || tokenIsIdentifier(type)) {\n      this.state.potentialArrowAt = this.state.start;\n    }\n\n    let left = this.parseMaybeConditional(refExpressionErrors);\n    if (afterLeftParse) {\n      left = afterLeftParse.call(this, left, startLoc);\n    }\n    if (tokenIsAssignment(this.state.type)) {\n      const node = this.startNodeAt<N.AssignmentExpression>(startLoc);\n      const operator = this.state.value;\n      node.operator = operator;\n\n      if (this.match(tt.eq)) {\n        this.toAssignable(left, /* isLHS */ true);\n        node.left = left;\n\n        const startIndex = startLoc.index;\n        if (\n          refExpressionErrors.doubleProtoLoc != null &&\n          refExpressionErrors.doubleProtoLoc.index >= startIndex\n        ) {\n          refExpressionErrors.doubleProtoLoc = null; // reset because double __proto__ is valid in assignment expression\n        }\n        if (\n          refExpressionErrors.shorthandAssignLoc != null &&\n          refExpressionErrors.shorthandAssignLoc.index >= startIndex\n        ) {\n          refExpressionErrors.shorthandAssignLoc = null; // reset because shorthand default was used correctly\n        }\n        if (\n          refExpressionErrors.privateKeyLoc != null &&\n          refExpressionErrors.privateKeyLoc.index >= startIndex\n        ) {\n          this.checkDestructuringPrivate(refExpressionErrors);\n          refExpressionErrors.privateKeyLoc = null; // reset because `({ #x: x })` is an assignable pattern\n        }\n      } else {\n        node.left = left;\n      }\n\n      this.next();\n      node.right = this.parseMaybeAssign();\n      this.checkLVal(left, {\n        in: this.finishNode(node, \"AssignmentExpression\"),\n      });\n      // @ts-expect-error todo(flow->ts) improve node types\n      return node;\n    } else if (ownExpressionErrors) {\n      this.checkExpressionErrors(refExpressionErrors, true);\n    }\n\n    return left;\n  }\n\n  // Parse a ternary conditional (`?:`) operator.\n  // https://tc39.es/ecma262/#prod-ConditionalExpression\n\n  parseMaybeConditional(\n    this: Parser,\n    refExpressionErrors: ExpressionErrors,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    const potentialArrowAt = this.state.potentialArrowAt;\n    const expr = this.parseExprOps(refExpressionErrors);\n\n    if (this.shouldExitDescending(expr, potentialArrowAt)) {\n      return expr;\n    }\n\n    return this.parseConditional(expr, startLoc, refExpressionErrors);\n  }\n\n  parseConditional(\n    this: Parser,\n    expr: N.Expression,\n    startLoc: Position,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.Expression {\n    if (this.eat(tt.question)) {\n      const node = this.startNodeAt(startLoc);\n      node.test = expr;\n      node.consequent = this.parseMaybeAssignAllowIn();\n      this.expect(tt.colon);\n      node.alternate = this.parseMaybeAssign();\n      return this.finishNode(node, \"ConditionalExpression\");\n    }\n    return expr;\n  }\n\n  parseMaybeUnaryOrPrivate(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors,\n  ): N.Expression | N.PrivateName {\n    return this.match(tt.privateName)\n      ? this.parsePrivateName()\n      : this.parseMaybeUnary(refExpressionErrors);\n  }\n\n  // Start the precedence parser.\n  // https://tc39.es/ecma262/#prod-ShortCircuitExpression\n\n  parseExprOps(\n    this: Parser,\n    refExpressionErrors: ExpressionErrors,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    const potentialArrowAt = this.state.potentialArrowAt;\n    const expr = this.parseMaybeUnaryOrPrivate(refExpressionErrors);\n\n    if (this.shouldExitDescending(expr, potentialArrowAt)) {\n      return expr;\n    }\n\n    return this.parseExprOp(expr, startLoc, -1);\n  }\n\n  // Parse binary operators with the operator precedence parsing\n  // algorithm. `left` is the left-hand side of the operator.\n  // `minPrec` provides context that allows the function to stop and\n  // defer further parser to one of its callers when it encounters an\n  // operator that has a lower precedence than the set it is parsing.\n\n  parseExprOp(\n    this: Parser,\n    left: N.Expression | N.PrivateName,\n    leftStartLoc: Position,\n    minPrec: number,\n  ): N.Expression {\n    if (this.isPrivateName(left)) {\n      // https://tc39.es/ecma262/#prod-RelationalExpression\n      // RelationalExpression [In, Yield, Await]\n      //   [+In] PrivateIdentifier in ShiftExpression[?Yield, ?Await]\n\n      const value = this.getPrivateNameSV(left);\n\n      if (\n        minPrec >= tokenOperatorPrecedence(tt._in) ||\n        !this.prodParam.hasIn ||\n        !this.match(tt._in)\n      ) {\n        this.raise(Errors.PrivateInExpectedIn, {\n          at: left,\n          identifierName: value,\n        });\n      }\n\n      this.classScope.usePrivateName(value, left.loc.start);\n    }\n\n    const op = this.state.type;\n    if (tokenIsOperator(op) && (this.prodParam.hasIn || !this.match(tt._in))) {\n      let prec = tokenOperatorPrecedence(op);\n      if (prec > minPrec) {\n        if (op === tt.pipeline) {\n          this.expectPlugin(\"pipelineOperator\");\n          if (this.state.inFSharpPipelineDirectBody) {\n            return left;\n          }\n          this.checkPipelineAtInfixOperator(left, leftStartLoc);\n        }\n        const node = this.startNodeAt<N.LogicalExpression | N.BinaryExpression>(\n          leftStartLoc,\n        );\n        node.left = left;\n        node.operator = this.state.value;\n\n        const logical = op === tt.logicalOR || op === tt.logicalAND;\n        const coalesce = op === tt.nullishCoalescing;\n\n        if (coalesce) {\n          // Handle the precedence of `tt.coalesce` as equal to the range of logical expressions.\n          // In other words, `node.right` shouldn't contain logical expressions in order to check the mixed error.\n          prec = tokenOperatorPrecedence(tt.logicalAND);\n        }\n\n        this.next();\n\n        if (\n          op === tt.pipeline &&\n          this.hasPlugin([\"pipelineOperator\", { proposal: \"minimal\" }])\n        ) {\n          if (this.state.type === tt._await && this.prodParam.hasAwait) {\n            throw this.raise(Errors.UnexpectedAwaitAfterPipelineBody, {\n              at: this.state.startLoc,\n            });\n          }\n        }\n\n        node.right = this.parseExprOpRightExpr(op, prec);\n        const finishedNode = this.finishNode(\n          node,\n          logical || coalesce ? \"LogicalExpression\" : \"BinaryExpression\",\n        );\n        /* this check is for all ?? operators\n         * a ?? b && c for this example\n         * when op is coalesce and nextOp is logical (&&), throw at the pos of nextOp that it can not be mixed.\n         * Symmetrically it also throws when op is logical and nextOp is coalesce\n         */\n        const nextOp = this.state.type;\n        if (\n          (coalesce && (nextOp === tt.logicalOR || nextOp === tt.logicalAND)) ||\n          (logical && nextOp === tt.nullishCoalescing)\n        ) {\n          throw this.raise(Errors.MixingCoalesceWithLogical, {\n            at: this.state.startLoc,\n          });\n        }\n\n        return this.parseExprOp(finishedNode, leftStartLoc, minPrec);\n      }\n    }\n    return left;\n  }\n\n  // Helper function for `parseExprOp`. Parse the right-hand side of binary-\n  // operator expressions, then apply any operator-specific functions.\n\n  parseExprOpRightExpr(\n    this: Parser,\n    op: TokenType,\n    prec: number,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    switch (op) {\n      case tt.pipeline:\n        switch (this.getPluginOption(\"pipelineOperator\", \"proposal\")) {\n          case \"hack\":\n            return this.withTopicBindingContext(() => {\n              return this.parseHackPipeBody();\n            });\n\n          case \"smart\":\n            return this.withTopicBindingContext(() => {\n              if (this.prodParam.hasYield && this.isContextual(tt._yield)) {\n                throw this.raise(Errors.PipeBodyIsTighter, {\n                  at: this.state.startLoc,\n                });\n              }\n              return this.parseSmartPipelineBodyInStyle(\n                this.parseExprOpBaseRightExpr(op, prec),\n                startLoc,\n              );\n            });\n\n          case \"fsharp\":\n            return this.withSoloAwaitPermittingContext(() => {\n              return this.parseFSharpPipelineBody(prec);\n            });\n        }\n\n      // Falls through.\n      default:\n        return this.parseExprOpBaseRightExpr(op, prec);\n    }\n  }\n\n  // Helper function for `parseExprOpRightExpr`. Parse the right-hand side of\n  // binary-operator expressions without applying any operator-specific functions.\n\n  parseExprOpBaseRightExpr(\n    this: Parser,\n    op: TokenType,\n    prec: number,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n\n    return this.parseExprOp(\n      this.parseMaybeUnaryOrPrivate(),\n      startLoc,\n      tokenIsRightAssociative(op) ? prec - 1 : prec,\n    );\n  }\n\n  parseHackPipeBody(this: Parser): N.Expression {\n    const { startLoc } = this.state;\n    const body = this.parseMaybeAssign();\n    const requiredParentheses = UnparenthesizedPipeBodyDescriptions.has(\n      // @ts-expect-error TS2345: Argument of type 'string' is not assignable to parameter of type '\"ArrowFunctionExpression\" | \"YieldExpression\" | \"AssignmentExpression\" | \"ConditionalExpression\"'.\n      body.type,\n    );\n\n    // TODO: Check how to handle type casts in Flow and TS once they are supported\n    if (requiredParentheses && !body.extra?.parenthesized) {\n      this.raise(Errors.PipeUnparenthesizedBody, {\n        at: startLoc,\n        // @ts-expect-error TS2322: Type 'string' is not assignable to type '\"AssignmentExpression\" | \"ArrowFunctionExpression\" | \"ConditionalExpression\" | \"YieldExpression\"'.\n        type: body.type,\n      });\n    }\n    if (!this.topicReferenceWasUsedInCurrentContext()) {\n      // A Hack pipe body must use the topic reference at least once.\n      this.raise(Errors.PipeTopicUnused, { at: startLoc });\n    }\n\n    return body;\n  }\n\n  checkExponentialAfterUnary(\n    node: N.AwaitExpression | Undone<N.UnaryExpression>,\n  ) {\n    if (this.match(tt.exponent)) {\n      this.raise(Errors.UnexpectedTokenUnaryExponentiation, {\n        at: node.argument,\n      });\n    }\n  }\n\n  // Parse unary operators, both prefix and postfix.\n  // https://tc39.es/ecma262/#prod-UnaryExpression\n  parseMaybeUnary(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n    sawUnary?: boolean,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    const isAwait = this.isContextual(tt._await);\n\n    if (isAwait && this.isAwaitAllowed()) {\n      this.next();\n      const expr = this.parseAwait(startLoc);\n      if (!sawUnary) this.checkExponentialAfterUnary(expr);\n      return expr;\n    }\n    const update = this.match(tt.incDec);\n    const node = this.startNode<N.UnaryExpression | N.UpdateExpression>();\n    if (tokenIsPrefix(this.state.type)) {\n      node.operator = this.state.value;\n      node.prefix = true;\n\n      if (this.match(tt._throw)) {\n        this.expectPlugin(\"throwExpressions\");\n      }\n      const isDelete = this.match(tt._delete);\n      this.next();\n\n      node.argument = this.parseMaybeUnary(null, true);\n\n      this.checkExpressionErrors(refExpressionErrors, true);\n\n      if (this.state.strict && isDelete) {\n        const arg = node.argument;\n\n        if (arg.type === \"Identifier\") {\n          this.raise(Errors.StrictDelete, { at: node });\n        } else if (this.hasPropertyAsPrivateName(arg)) {\n          this.raise(Errors.DeletePrivateField, { at: node });\n        }\n      }\n\n      if (!update) {\n        if (!sawUnary) {\n          this.checkExponentialAfterUnary(node as Undone<N.UnaryExpression>);\n        }\n        return this.finishNode(node, \"UnaryExpression\");\n      }\n    }\n\n    const expr = this.parseUpdate(\n      // @ts-expect-error using \"Undone\" node as \"done\"\n      node,\n      update,\n      refExpressionErrors,\n    );\n\n    if (isAwait) {\n      const { type } = this.state;\n      const startsExpr = this.hasPlugin(\"v8intrinsic\")\n        ? tokenCanStartExpression(type)\n        : tokenCanStartExpression(type) && !this.match(tt.modulo);\n      if (startsExpr && !this.isAmbiguousAwait()) {\n        this.raiseOverwrite(Errors.AwaitNotInAsyncContext, { at: startLoc });\n        return this.parseAwait(startLoc);\n      }\n    }\n\n    return expr;\n  }\n\n  // https://tc39.es/ecma262/#prod-UpdateExpression\n  parseUpdate(\n    this: Parser,\n    node: N.Expression,\n    update: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.Expression {\n    if (update) {\n      // @ts-expect-error Type 'Node' is missing the following properties from type 'Undone<UpdateExpression>': prefix, operator, argument\n      const updateExpressionNode = node as Undone<N.UpdateExpression>;\n      this.checkLVal(updateExpressionNode.argument, {\n        in: this.finishNode(updateExpressionNode, \"UpdateExpression\"),\n      });\n      return node;\n    }\n\n    const startLoc = this.state.startLoc;\n    let expr = this.parseExprSubscripts(refExpressionErrors);\n    if (this.checkExpressionErrors(refExpressionErrors, false)) return expr;\n    while (tokenIsPostfix(this.state.type) && !this.canInsertSemicolon()) {\n      const node = this.startNodeAt<N.UpdateExpression>(startLoc);\n      node.operator = this.state.value;\n      node.prefix = false;\n      node.argument = expr;\n      this.next();\n      this.checkLVal(expr, {\n        in: (expr = this.finishNode(node, \"UpdateExpression\")),\n      });\n    }\n    return expr;\n  }\n\n  // Parse call, dot, and `[]`-subscript expressions.\n  // https://tc39.es/ecma262/#prod-LeftHandSideExpression\n  parseExprSubscripts(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n    const potentialArrowAt = this.state.potentialArrowAt;\n    const expr = this.parseExprAtom(refExpressionErrors);\n\n    if (this.shouldExitDescending(expr, potentialArrowAt)) {\n      return expr;\n    }\n\n    return this.parseSubscripts(expr, startLoc);\n  }\n\n  parseSubscripts(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    noCalls?: boolean | null,\n  ): N.Expression {\n    const state = {\n      optionalChainMember: false,\n      maybeAsyncArrow: this.atPossibleAsyncArrow(base),\n      stop: false,\n    };\n    do {\n      base = this.parseSubscript(base, startLoc, noCalls, state);\n\n      // After parsing a subscript, this isn't \"async\" for sure.\n      state.maybeAsyncArrow = false;\n    } while (!state.stop);\n    return base;\n  }\n\n  /**\n   * @param state Set 'state.stop = true' to indicate that we should stop parsing subscripts.\n   *   state.optionalChainMember to indicate that the member is currently in OptionalChain\n   */\n  parseSubscript(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    noCalls: boolean | undefined | null,\n    state: N.ParseSubscriptState,\n  ): N.Expression {\n    const { type } = this.state;\n    if (!noCalls && type === tt.doubleColon) {\n      return this.parseBind(base, startLoc, noCalls, state);\n    } else if (tokenIsTemplate(type)) {\n      return this.parseTaggedTemplateExpression(base, startLoc, state);\n    }\n\n    let optional = false;\n\n    if (type === tt.questionDot) {\n      if (noCalls) {\n        this.raise(Errors.OptionalChainingNoNew, {\n          at: this.state.startLoc,\n        });\n        if (this.lookaheadCharCode() === charCodes.leftParenthesis) {\n          // stop at `?.` when parsing `new a?.()`\n          state.stop = true;\n          return base;\n        }\n      }\n      state.optionalChainMember = optional = true;\n      this.next();\n    }\n\n    if (!noCalls && this.match(tt.parenL)) {\n      return this.parseCoverCallAndAsyncArrowHead(\n        base,\n        startLoc,\n        state,\n        optional,\n      );\n    } else {\n      const computed = this.eat(tt.bracketL);\n      if (computed || optional || this.eat(tt.dot)) {\n        return this.parseMember(base, startLoc, state, computed, optional);\n      } else {\n        state.stop = true;\n        return base;\n      }\n    }\n  }\n\n  // base[?Yield, ?Await] [ Expression[+In, ?Yield, ?Await] ]\n  // base[?Yield, ?Await] . IdentifierName\n  // base[?Yield, ?Await] . PrivateIdentifier\n  //   where `base` is one of CallExpression, MemberExpression and OptionalChain\n  parseMember(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    state: N.ParseSubscriptState,\n    computed: boolean,\n    optional: boolean,\n  ): N.OptionalMemberExpression | N.MemberExpression {\n    const node = this.startNodeAt<\n      N.OptionalMemberExpression | N.MemberExpression\n    >(startLoc);\n    node.object = base;\n    node.computed = computed;\n    if (computed) {\n      node.property = this.parseExpression();\n      this.expect(tt.bracketR);\n    } else if (this.match(tt.privateName)) {\n      if (base.type === \"Super\") {\n        this.raise(Errors.SuperPrivateField, { at: startLoc });\n      }\n      this.classScope.usePrivateName(this.state.value, this.state.startLoc);\n      node.property = this.parsePrivateName();\n    } else {\n      node.property = this.parseIdentifier(true);\n    }\n\n    if (state.optionalChainMember) {\n      (node as N.OptionalMemberExpression).optional = optional;\n      return this.finishNode(node, \"OptionalMemberExpression\");\n    } else {\n      return this.finishNode(node, \"MemberExpression\");\n    }\n  }\n\n  // https://github.com/tc39/proposal-bind-operator#syntax\n  parseBind(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    noCalls: boolean | undefined | null,\n    state: N.ParseSubscriptState,\n  ): N.Expression {\n    const node = this.startNodeAt(startLoc);\n    node.object = base;\n    this.next(); // eat '::'\n    node.callee = this.parseNoCallExpr();\n    state.stop = true;\n    return this.parseSubscripts(\n      this.finishNode(node, \"BindExpression\"),\n      startLoc,\n      noCalls,\n    );\n  }\n\n  // https://tc39.es/ecma262/#prod-CoverCallExpressionAndAsyncArrowHead\n  // CoverCallExpressionAndAsyncArrowHead\n  // CallExpression[?Yield, ?Await] Arguments[?Yield, ?Await]\n  // OptionalChain[?Yield, ?Await] Arguments[?Yield, ?Await]\n  parseCoverCallAndAsyncArrowHead(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    state: N.ParseSubscriptState,\n    optional: boolean,\n  ): N.Expression {\n    const oldMaybeInArrowParameters = this.state.maybeInArrowParameters;\n    let refExpressionErrors: ExpressionErrors | null = null;\n\n    this.state.maybeInArrowParameters = true;\n    this.next(); // eat `(`\n\n    const node = this.startNodeAt<N.CallExpression | N.OptionalCallExpression>(\n      startLoc,\n    );\n    node.callee = base;\n    const { maybeAsyncArrow, optionalChainMember } = state;\n\n    if (maybeAsyncArrow) {\n      this.expressionScope.enter(newAsyncArrowScope());\n      refExpressionErrors = new ExpressionErrors();\n    }\n\n    if (optionalChainMember) {\n      // @ts-expect-error when optionalChainMember is true, node must be an optional call\n      node.optional = optional;\n    }\n\n    if (optional) {\n      node.arguments = this.parseCallExpressionArguments(tt.parenR);\n    } else {\n      node.arguments = this.parseCallExpressionArguments(\n        tt.parenR,\n        base.type === \"Import\",\n        base.type !== \"Super\",\n        // @ts-expect-error todo(flow->ts)\n        node,\n        refExpressionErrors,\n      );\n    }\n    let finishedNode:\n      | N.CallExpression\n      | N.OptionalCallExpression\n      | N.ArrowFunctionExpression = this.finishCallExpression(\n      node,\n      optionalChainMember,\n    );\n\n    if (maybeAsyncArrow && this.shouldParseAsyncArrow() && !optional) {\n      /*:: invariant(refExpressionErrors != null) */\n      state.stop = true;\n      this.checkDestructuringPrivate(refExpressionErrors);\n      this.expressionScope.validateAsPattern();\n      this.expressionScope.exit();\n      finishedNode = this.parseAsyncArrowFromCallExpression(\n        this.startNodeAt<N.ArrowFunctionExpression>(startLoc),\n        finishedNode as N.CallExpression,\n      );\n    } else {\n      if (maybeAsyncArrow) {\n        this.checkExpressionErrors(refExpressionErrors, true);\n        this.expressionScope.exit();\n      }\n      this.toReferencedArguments(finishedNode);\n    }\n\n    this.state.maybeInArrowParameters = oldMaybeInArrowParameters;\n\n    return finishedNode;\n  }\n\n  toReferencedArguments(\n    node: N.CallExpression | N.OptionalCallExpression,\n    isParenthesizedExpr?: boolean,\n  ) {\n    this.toReferencedListDeep(node.arguments, isParenthesizedExpr);\n  }\n\n  // MemberExpression [?Yield, ?Await] TemplateLiteral[?Yield, ?Await, +Tagged]\n  // CallExpression [?Yield, ?Await] TemplateLiteral[?Yield, ?Await, +Tagged]\n  parseTaggedTemplateExpression(\n    this: Parser,\n    base: N.Expression,\n    startLoc: Position,\n    state: N.ParseSubscriptState,\n  ): N.TaggedTemplateExpression {\n    const node = this.startNodeAt<N.TaggedTemplateExpression>(startLoc);\n    node.tag = base;\n    node.quasi = this.parseTemplate(true);\n    if (state.optionalChainMember) {\n      this.raise(Errors.OptionalChainingNoTemplate, { at: startLoc });\n    }\n    return this.finishNode(node, \"TaggedTemplateExpression\");\n  }\n\n  atPossibleAsyncArrow(base: N.Expression): boolean {\n    return (\n      base.type === \"Identifier\" &&\n      base.name === \"async\" &&\n      this.state.lastTokEndLoc.index === base.end &&\n      !this.canInsertSemicolon() &&\n      // check there are no escape sequences, such as \\u{61}sync\n      base.end - base.start === 5 &&\n      base.start === this.state.potentialArrowAt\n    );\n  }\n\n  expectImportAttributesPlugin() {\n    if (!this.hasPlugin(\"importAssertions\")) {\n      this.expectPlugin(\"importAttributes\");\n    }\n  }\n\n  finishCallExpression<T extends N.CallExpression | N.OptionalCallExpression>(\n    node: Undone<T>,\n    optional: boolean,\n  ): T {\n    if (node.callee.type === \"Import\") {\n      if (node.arguments.length === 2) {\n        if (process.env.BABEL_8_BREAKING) {\n          this.expectImportAttributesPlugin();\n        } else {\n          if (!this.hasPlugin(\"moduleAttributes\")) {\n            this.expectImportAttributesPlugin();\n          }\n        }\n      }\n      if (node.arguments.length === 0 || node.arguments.length > 2) {\n        this.raise(Errors.ImportCallArity, {\n          at: node,\n          maxArgumentCount:\n            this.hasPlugin(\"importAttributes\") ||\n            this.hasPlugin(\"importAssertions\") ||\n            this.hasPlugin(\"moduleAttributes\")\n              ? 2\n              : 1,\n        });\n      } else {\n        for (const arg of node.arguments) {\n          if (arg.type === \"SpreadElement\") {\n            this.raise(Errors.ImportCallSpreadArgument, { at: arg });\n          }\n        }\n      }\n    }\n    return this.finishNode(\n      node,\n      optional ? \"OptionalCallExpression\" : \"CallExpression\",\n    );\n  }\n\n  parseCallExpressionArguments(\n    this: Parser,\n    close: TokenType,\n    dynamicImport?: boolean,\n    allowPlaceholder?: boolean,\n    nodeForExtra?: N.Node | null,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): Array<N.Expression | undefined | null> {\n    const elts: N.Expression[] = [];\n    let first = true;\n    const oldInFSharpPipelineDirectBody = this.state.inFSharpPipelineDirectBody;\n    this.state.inFSharpPipelineDirectBody = false;\n\n    while (!this.eat(close)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(tt.comma);\n        if (this.match(close)) {\n          if (\n            dynamicImport &&\n            !this.hasPlugin(\"importAttributes\") &&\n            !this.hasPlugin(\"importAssertions\") &&\n            !this.hasPlugin(\"moduleAttributes\")\n          ) {\n            this.raise(Errors.ImportCallArgumentTrailingComma, {\n              at: this.state.lastTokStartLoc,\n            });\n          }\n          if (nodeForExtra) {\n            this.addTrailingCommaExtraToNode(nodeForExtra);\n          }\n          this.next();\n          break;\n        }\n      }\n\n      elts.push(\n        this.parseExprListItem(false, refExpressionErrors, allowPlaceholder),\n      );\n    }\n\n    this.state.inFSharpPipelineDirectBody = oldInFSharpPipelineDirectBody;\n\n    return elts;\n  }\n\n  shouldParseAsyncArrow(): boolean {\n    return this.match(tt.arrow) && !this.canInsertSemicolon();\n  }\n\n  parseAsyncArrowFromCallExpression(\n    this: Parser,\n    node: Undone<N.ArrowFunctionExpression>,\n    call: N.CallExpression,\n  ): N.ArrowFunctionExpression {\n    this.resetPreviousNodeTrailingComments(call);\n    this.expect(tt.arrow);\n    this.parseArrowExpression(\n      node,\n      call.arguments,\n      true,\n      call.extra?.trailingCommaLoc,\n    );\n    // mark inner comments of `async()` as inner comments of `async () =>`\n    if (call.innerComments) {\n      setInnerComments(node, call.innerComments);\n    }\n    // mark trailing comments of `async` to be inner comments\n    if (call.callee.trailingComments) {\n      setInnerComments(node, call.callee.trailingComments);\n    }\n    return node as N.ArrowFunctionExpression;\n  }\n\n  // Parse a no-call expression (like argument of `new` or `::` operators).\n  // https://tc39.es/ecma262/#prod-MemberExpression\n  parseNoCallExpr(this: Parser): N.Expression {\n    const startLoc = this.state.startLoc;\n    return this.parseSubscripts(this.parseExprAtom(), startLoc, true);\n  }\n\n  // Parse an atomic expression — either a single token that is an\n  // expression, an expression started by a keyword like `function` or\n  // `new`, or an expression wrapped in punctuation like `()`, `[]`,\n  // or `{}`.\n\n  // https://tc39.es/ecma262/#prod-PrimaryExpression\n  // https://tc39.es/ecma262/#prod-AsyncArrowFunction\n  // PrimaryExpression\n  // Super\n  // Import\n  // AsyncArrowFunction\n\n  parseExprAtom(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.Expression {\n    let node;\n    let decorators: N.Decorator[] | null = null;\n\n    const { type } = this.state;\n    switch (type) {\n      case tt._super:\n        return this.parseSuper();\n\n      case tt._import:\n        node = this.startNode<N.MetaProperty | N.Import>();\n        this.next();\n\n        if (this.match(tt.dot)) {\n          return this.parseImportMetaProperty(node as Undone<N.MetaProperty>);\n        }\n\n        if (!this.match(tt.parenL)) {\n          this.raise(Errors.UnsupportedImport, {\n            at: this.state.lastTokStartLoc,\n          });\n        }\n        return this.finishNode(node, \"Import\");\n      case tt._this:\n        node = this.startNode();\n        this.next();\n        return this.finishNode(node, \"ThisExpression\");\n\n      case tt._do: {\n        return this.parseDo(this.startNode(), false);\n      }\n\n      case tt.slash:\n      case tt.slashAssign: {\n        this.readRegexp();\n        return this.parseRegExpLiteral(this.state.value);\n      }\n\n      case tt.num:\n        return this.parseNumericLiteral(this.state.value);\n\n      case tt.bigint:\n        return this.parseBigIntLiteral(this.state.value);\n\n      case tt.decimal:\n        return this.parseDecimalLiteral(this.state.value);\n\n      case tt.string:\n        return this.parseStringLiteral(this.state.value);\n\n      case tt._null:\n        return this.parseNullLiteral();\n\n      case tt._true:\n        return this.parseBooleanLiteral(true);\n      case tt._false:\n        return this.parseBooleanLiteral(false);\n\n      case tt.parenL: {\n        const canBeArrow = this.state.potentialArrowAt === this.state.start;\n        return this.parseParenAndDistinguishExpression(canBeArrow);\n      }\n\n      case tt.bracketBarL:\n      case tt.bracketHashL: {\n        return this.parseArrayLike(\n          this.state.type === tt.bracketBarL ? tt.bracketBarR : tt.bracketR,\n          /* canBePattern */ false,\n          /* isTuple */ true,\n        );\n      }\n      case tt.bracketL: {\n        return this.parseArrayLike(\n          tt.bracketR,\n          /* canBePattern */ true,\n          /* isTuple */ false,\n          refExpressionErrors,\n        );\n      }\n      case tt.braceBarL:\n      case tt.braceHashL: {\n        return this.parseObjectLike(\n          this.state.type === tt.braceBarL ? tt.braceBarR : tt.braceR,\n          /* isPattern */ false,\n          /* isRecord */ true,\n        );\n      }\n      case tt.braceL: {\n        return this.parseObjectLike(\n          tt.braceR,\n          /* isPattern */ false,\n          /* isRecord */ false,\n          refExpressionErrors,\n        );\n      }\n      case tt._function:\n        return this.parseFunctionOrFunctionSent();\n\n      case tt.at:\n        decorators = this.parseDecorators();\n      // fall through\n      case tt._class:\n        return this.parseClass(\n          this.maybeTakeDecorators(decorators, this.startNode()),\n          false,\n        );\n\n      case tt._new:\n        return this.parseNewOrNewTarget();\n\n      case tt.templateNonTail:\n      case tt.templateTail:\n        return this.parseTemplate(false);\n\n      // BindExpression[Yield]\n      //   :: MemberExpression[?Yield]\n      case tt.doubleColon: {\n        node = this.startNode();\n        this.next();\n        node.object = null;\n        const callee = (node.callee = this.parseNoCallExpr());\n        if (callee.type === \"MemberExpression\") {\n          return this.finishNode(node, \"BindExpression\");\n        } else {\n          throw this.raise(Errors.UnsupportedBind, { at: callee });\n        }\n      }\n\n      case tt.privateName: {\n        // Standalone private names are only allowed in \"#x in obj\"\n        // expressions, and they are directly handled by callers of\n        // parseExprOp. If we reach this, the input is always invalid.\n        // We can throw a better error message and recover, rather than\n        // just throwing \"Unexpected token\" (which is the default\n        // behavior of this big switch statement).\n        this.raise(Errors.PrivateInExpectedIn, {\n          at: this.state.startLoc,\n          identifierName: this.state.value,\n        });\n        return this.parsePrivateName();\n      }\n\n      case tt.moduloAssign: {\n        return this.parseTopicReferenceThenEqualsSign(tt.modulo, \"%\");\n      }\n\n      case tt.xorAssign: {\n        return this.parseTopicReferenceThenEqualsSign(tt.bitwiseXOR, \"^\");\n      }\n\n      case tt.doubleCaret:\n      case tt.doubleAt: {\n        return this.parseTopicReference(\"hack\");\n      }\n\n      case tt.bitwiseXOR:\n      case tt.modulo:\n      case tt.hash: {\n        const pipeProposal = this.getPluginOption(\n          \"pipelineOperator\",\n          \"proposal\",\n        );\n\n        if (pipeProposal) {\n          return this.parseTopicReference(pipeProposal);\n        }\n        this.unexpected();\n        break;\n      }\n\n      case tt.lt: {\n        const lookaheadCh = this.input.codePointAt(this.nextTokenStart());\n        if (\n          isIdentifierStart(lookaheadCh) || // Element/Type Parameter <foo>\n          lookaheadCh === charCodes.greaterThan // Fragment <>\n        ) {\n          this.expectOnePlugin([\"jsx\", \"flow\", \"typescript\"]);\n        } else {\n          this.unexpected();\n        }\n        break;\n      }\n\n      default:\n        if (tokenIsIdentifier(type)) {\n          if (\n            this.isContextual(tt._module) &&\n            this.lookaheadInLineCharCode() === charCodes.leftCurlyBrace\n          ) {\n            return this.parseModuleExpression();\n          }\n          const canBeArrow = this.state.potentialArrowAt === this.state.start;\n          const containsEsc = this.state.containsEsc;\n          const id = this.parseIdentifier();\n\n          if (\n            !containsEsc &&\n            id.name === \"async\" &&\n            !this.canInsertSemicolon()\n          ) {\n            const { type } = this.state;\n            if (type === tt._function) {\n              this.resetPreviousNodeTrailingComments(id);\n              this.next();\n              return this.parseAsyncFunctionExpression(\n                this.startNodeAtNode(id),\n              );\n            } else if (tokenIsIdentifier(type)) {\n              // If the next token begins with \"=\", commit to parsing an async\n              // arrow function. (Peeking ahead for \"=\" lets us avoid a more\n              // expensive full-token lookahead on this common path.)\n              if (this.lookaheadCharCode() === charCodes.equalsTo) {\n                // although `id` is not used in async arrow unary function,\n                // we don't need to reset `async`'s trailing comments because\n                // it will be attached to the upcoming async arrow binding identifier\n                return this.parseAsyncArrowUnaryFunction(\n                  this.startNodeAtNode(id),\n                );\n              } else {\n                // Otherwise, treat \"async\" as an identifier and let calling code\n                // deal with the current tt.name token.\n                return id;\n              }\n            } else if (type === tt._do) {\n              this.resetPreviousNodeTrailingComments(id);\n              return this.parseDo(this.startNodeAtNode(id), true);\n            }\n          }\n\n          if (\n            canBeArrow &&\n            this.match(tt.arrow) &&\n            !this.canInsertSemicolon()\n          ) {\n            this.next();\n            return this.parseArrowExpression(\n              this.startNodeAtNode(id),\n              [id],\n              false,\n            );\n          }\n\n          return id;\n        } else {\n          this.unexpected();\n        }\n    }\n  }\n\n  // This helper method should only be called\n  // when the parser has reached a potential Hack pipe topic token\n  // that is followed by an equals sign.\n  // See <https://github.com/js-choi/proposal-hack-pipes>.\n  // If we find ^= or %= in an expression position\n  // (i.e., the tt.moduloAssign or tt.xorAssign token types), and if the\n  // Hack-pipes proposal is active with ^ or % as its topicToken, then the ^ or\n  // % could be the topic token (e.g., in x |> ^==y or x |> ^===y), and so we\n  // reparse the current token as ^ or %.\n  // Otherwise, this throws an unexpected-token error.\n  parseTopicReferenceThenEqualsSign(\n    topicTokenType: TokenType,\n    topicTokenValue: string,\n  ): N.Expression {\n    const pipeProposal = this.getPluginOption(\"pipelineOperator\", \"proposal\");\n\n    if (pipeProposal) {\n      // Set the most-recent token to be a topic token\n      // given by the tokenType and tokenValue.\n      // Now the next readToken() call (in parseTopicReference)\n      // will consume that “topic token”.\n      this.state.type = topicTokenType;\n      this.state.value = topicTokenValue;\n      // Rewind the tokenizer to the end of the “topic token”, so that the\n      // following token starts at the equals sign after that topic token.\n      this.state.pos--;\n      this.state.end--;\n      // This is safe to do since the preceding character was either ^ or %, and\n      // thus not a newline.\n      this.state.endLoc = createPositionWithColumnOffset(this.state.endLoc, -1);\n      // Now actually consume the topic token.\n      return this.parseTopicReference(pipeProposal);\n    } else {\n      this.unexpected();\n    }\n  }\n\n  // This helper method should only be called\n  // when the proposal-pipeline-operator plugin is active,\n  // and when the parser has reached a potential Hack pipe topic token.\n  // Although a pipe-operator proposal is assumed to be active,\n  // its configuration might not match the current token’s type.\n  // See <https://github.com/js-choi/proposal-hack-pipes>.\n  parseTopicReference(pipeProposal: string): N.Expression {\n    const node = this.startNode<N.TopicReference>();\n    const startLoc = this.state.startLoc;\n    const tokenType = this.state.type;\n\n    // Consume the current token.\n    this.next();\n\n    // If the pipe-operator plugin’s configuration matches the current token’s type,\n    // then this will return `node`, will have been finished as a topic reference.\n    // Otherwise, this will throw a `PipeTopicUnconfiguredToken` error.\n    return this.finishTopicReference(node, startLoc, pipeProposal, tokenType);\n  }\n\n  // This helper method attempts to finish the given `node`\n  // into a topic-reference node for the given `pipeProposal`.\n  // See <https://github.com/js-choi/proposal-hack-pipes>.\n  //\n  // The method assumes that any topic token was consumed before it was called.\n  //\n  // If the `pipelineOperator` plugin is active,\n  // and if the given `tokenType` matches the plugin’s configuration,\n  // then this method will return the finished `node`.\n  //\n  // If the `pipelineOperator` plugin is active,\n  // but if the given `tokenType` does not match the plugin’s configuration,\n  // then this method will throw a `PipeTopicUnconfiguredToken` error.\n  finishTopicReference(\n    node: Undone<N.Node>,\n    startLoc: Position,\n    pipeProposal: string,\n    tokenType: TokenType,\n  ): N.Expression {\n    if (\n      this.testTopicReferenceConfiguration(pipeProposal, startLoc, tokenType)\n    ) {\n      // The token matches the plugin’s configuration.\n      // The token is therefore a topic reference.\n\n      // Determine the node type for the topic reference\n      // that is appropriate for the active pipe-operator proposal.\n      const nodeType =\n        pipeProposal === \"smart\"\n          ? \"PipelinePrimaryTopicReference\"\n          : // The proposal must otherwise be \"hack\",\n            // as enforced by testTopicReferenceConfiguration.\n            \"TopicReference\";\n\n      if (!this.topicReferenceIsAllowedInCurrentContext()) {\n        this.raise(\n          // The topic reference is not allowed in the current context:\n          // it is outside of a pipe body.\n          // Raise recoverable errors.\n          pipeProposal === \"smart\"\n            ? Errors.PrimaryTopicNotAllowed\n            : // In this case, `pipeProposal === \"hack\"` is true.\n              Errors.PipeTopicUnbound,\n          { at: startLoc },\n        );\n      }\n\n      // Register the topic reference so that its pipe body knows\n      // that its topic was used at least once.\n      this.registerTopicReference();\n\n      return this.finishNode(node, nodeType);\n    } else {\n      // The token does not match the plugin’s configuration.\n      throw this.raise(Errors.PipeTopicUnconfiguredToken, {\n        at: startLoc,\n        token: tokenLabelName(tokenType),\n      });\n    }\n  }\n\n  // This helper method tests whether the given token type\n  // matches the pipelineOperator parser plugin’s configuration.\n  // If the active pipe proposal is Hack style,\n  // and if the given token is the same as the plugin configuration’s `topicToken`,\n  // then this is a valid topic reference.\n  // If the active pipe proposal is smart mix,\n  // then the topic token must always be `#`.\n  // If the active pipe proposal is neither (e.g., \"minimal\" or \"fsharp\"),\n  // then an error is thrown.\n  testTopicReferenceConfiguration(\n    pipeProposal: string,\n    startLoc: Position,\n    tokenType: TokenType,\n  ): boolean {\n    switch (pipeProposal) {\n      case \"hack\": {\n        return this.hasPlugin([\n          \"pipelineOperator\",\n          {\n            // @ts-expect-error token must have a label\n            topicToken: tokenLabelName(tokenType),\n          },\n        ]);\n      }\n      case \"smart\":\n        return tokenType === tt.hash;\n      default:\n        throw this.raise(Errors.PipeTopicRequiresHackPipes, { at: startLoc });\n    }\n  }\n\n  // async [no LineTerminator here] AsyncArrowBindingIdentifier[?Yield] [no LineTerminator here] => AsyncConciseBody[?In]\n  parseAsyncArrowUnaryFunction(\n    this: Parser,\n    node: Undone<N.ArrowFunctionExpression>,\n  ): N.ArrowFunctionExpression {\n    // We don't need to push a new ParameterDeclarationScope here since we are sure\n    // 1) it is an async arrow, 2) no biding pattern is allowed in params\n    this.prodParam.enter(functionFlags(true, this.prodParam.hasYield));\n    const params = [this.parseIdentifier()];\n    this.prodParam.exit();\n    if (this.hasPrecedingLineBreak()) {\n      this.raise(Errors.LineTerminatorBeforeArrow, {\n        at: this.state.curPosition(),\n      });\n    }\n    this.expect(tt.arrow);\n    // let foo = async bar => {};\n    return this.parseArrowExpression(node, params, true);\n  }\n\n  // https://github.com/tc39/proposal-do-expressions\n  // https://github.com/tc39/proposal-async-do-expressions\n  parseDo(\n    this: Parser,\n    node: Undone<N.DoExpression>,\n    isAsync: boolean,\n  ): N.DoExpression {\n    this.expectPlugin(\"doExpressions\");\n    if (isAsync) {\n      this.expectPlugin(\"asyncDoExpressions\");\n    }\n    node.async = isAsync;\n    this.next(); // eat `do`\n    const oldLabels = this.state.labels;\n    this.state.labels = [];\n    if (isAsync) {\n      // AsyncDoExpression :\n      // async [no LineTerminator here] do Block[~Yield, +Await, ~Return]\n      this.prodParam.enter(PARAM_AWAIT);\n      node.body = this.parseBlock();\n      this.prodParam.exit();\n    } else {\n      node.body = this.parseBlock();\n    }\n\n    this.state.labels = oldLabels;\n    return this.finishNode(node, \"DoExpression\");\n  }\n\n  // Parse the `super` keyword\n  parseSuper(): N.Super {\n    const node = this.startNode<N.Super>();\n    this.next(); // eat `super`\n    if (\n      this.match(tt.parenL) &&\n      !this.scope.allowDirectSuper &&\n      !this.options.allowSuperOutsideMethod\n    ) {\n      this.raise(Errors.SuperNotAllowed, { at: node });\n    } else if (\n      !this.scope.allowSuper &&\n      !this.options.allowSuperOutsideMethod\n    ) {\n      this.raise(Errors.UnexpectedSuper, { at: node });\n    }\n\n    if (\n      !this.match(tt.parenL) &&\n      !this.match(tt.bracketL) &&\n      !this.match(tt.dot)\n    ) {\n      this.raise(Errors.UnsupportedSuper, { at: node });\n    }\n\n    return this.finishNode(node, \"Super\");\n  }\n\n  parsePrivateName(): N.PrivateName {\n    const node = this.startNode<N.PrivateName>();\n    const id = this.startNodeAt<N.Identifier>(\n      // The position is hardcoded because we merge `#` and name into a single\n      // tt.privateName token\n      createPositionWithColumnOffset(this.state.startLoc, 1),\n    );\n    const name = this.state.value;\n    this.next(); // eat #name;\n    node.id = this.createIdentifier(id, name);\n    return this.finishNode(node, \"PrivateName\");\n  }\n\n  parseFunctionOrFunctionSent(\n    this: Parser,\n  ): N.FunctionExpression | N.MetaProperty {\n    const node = this.startNode<N.FunctionExpression | N.MetaProperty>();\n\n    // We do not do parseIdentifier here because when parseFunctionOrFunctionSent\n    // is called we already know that the current token is a \"name\" with the value \"function\"\n    // This will improve perf a tiny little bit as we do not do validation but more importantly\n    // here is that parseIdentifier will remove an item from the expression stack\n    // if \"function\" or \"class\" is parsed as identifier (in objects e.g.), which should not happen here.\n    this.next(); // eat `function`\n\n    if (this.prodParam.hasYield && this.match(tt.dot)) {\n      const meta = this.createIdentifier(\n        this.startNodeAtNode<N.Identifier>(node),\n        \"function\",\n      );\n      this.next(); // eat `.`\n      // https://github.com/tc39/proposal-function.sent#syntax-1\n      if (this.match(tt._sent)) {\n        this.expectPlugin(\"functionSent\");\n      } else if (!this.hasPlugin(\"functionSent\")) {\n        // The code wasn't `function.sent` but just `function.`, so a simple error is less confusing.\n        this.unexpected();\n      }\n      return this.parseMetaProperty(\n        node as Undone<N.MetaProperty>,\n        meta,\n        \"sent\",\n      );\n    }\n    return this.parseFunction(node as Undone<N.FunctionExpression>);\n  }\n\n  parseMetaProperty(\n    node: Undone<N.MetaProperty>,\n    meta: N.Identifier,\n    propertyName: string,\n  ): N.MetaProperty {\n    node.meta = meta;\n\n    const containsEsc = this.state.containsEsc;\n\n    node.property = this.parseIdentifier(true);\n\n    if (node.property.name !== propertyName || containsEsc) {\n      this.raise(Errors.UnsupportedMetaProperty, {\n        at: node.property,\n        target: meta.name,\n        onlyValidPropertyName: propertyName,\n      });\n    }\n\n    return this.finishNode(node, \"MetaProperty\");\n  }\n\n  // https://tc39.es/ecma262/#prod-ImportMeta\n  parseImportMetaProperty(node: Undone<N.MetaProperty>): N.MetaProperty {\n    const id = this.createIdentifier(\n      this.startNodeAtNode<N.Identifier>(node),\n      \"import\",\n    );\n    this.next(); // eat `.`\n\n    if (this.isContextual(tt._meta)) {\n      if (!this.inModule) {\n        this.raise(Errors.ImportMetaOutsideModule, { at: id });\n      }\n      this.sawUnambiguousESM = true;\n    }\n\n    return this.parseMetaProperty(node, id, \"meta\");\n  }\n\n  parseLiteralAtNode<T extends N.Node>(\n    value: any,\n    type: T[\"type\"],\n    node: any,\n  ): T {\n    this.addExtra(node, \"rawValue\", value);\n    this.addExtra(node, \"raw\", this.input.slice(node.start, this.state.end));\n    node.value = value;\n    this.next();\n    return this.finishNode<T>(node, type);\n  }\n\n  parseLiteral<T extends N.Node>(value: any, type: T[\"type\"]): T {\n    const node = this.startNode();\n    return this.parseLiteralAtNode(value, type, node);\n  }\n\n  parseStringLiteral(value: any) {\n    return this.parseLiteral<N.StringLiteral>(value, \"StringLiteral\");\n  }\n\n  parseNumericLiteral(value: any) {\n    return this.parseLiteral<N.NumericLiteral>(value, \"NumericLiteral\");\n  }\n\n  parseBigIntLiteral(value: any) {\n    return this.parseLiteral<N.BigIntLiteral>(value, \"BigIntLiteral\");\n  }\n\n  parseDecimalLiteral(value: any) {\n    return this.parseLiteral<N.DecimalLiteral>(value, \"DecimalLiteral\");\n  }\n\n  parseRegExpLiteral(value: {\n    value: any;\n    pattern: string;\n    flags: N.RegExpLiteral[\"flags\"];\n  }) {\n    const node = this.parseLiteral<N.RegExpLiteral>(\n      value.value,\n      \"RegExpLiteral\",\n    );\n    node.pattern = value.pattern;\n    node.flags = value.flags;\n    return node;\n  }\n\n  parseBooleanLiteral(value: boolean) {\n    const node = this.startNode<N.BooleanLiteral>();\n    node.value = value;\n    this.next();\n    return this.finishNode(node, \"BooleanLiteral\");\n  }\n\n  parseNullLiteral() {\n    const node = this.startNode<N.NullLiteral>();\n    this.next();\n    return this.finishNode(node, \"NullLiteral\");\n  }\n\n  // https://tc39.es/ecma262/#prod-CoverParenthesizedExpressionAndArrowParameterList\n  parseParenAndDistinguishExpression(\n    this: Parser,\n    canBeArrow: boolean,\n  ): N.Expression {\n    const startLoc = this.state.startLoc;\n\n    let val;\n    this.next(); // eat `(`\n    this.expressionScope.enter(newArrowHeadScope());\n\n    const oldMaybeInArrowParameters = this.state.maybeInArrowParameters;\n    const oldInFSharpPipelineDirectBody = this.state.inFSharpPipelineDirectBody;\n    this.state.maybeInArrowParameters = true;\n    this.state.inFSharpPipelineDirectBody = false;\n\n    const innerStartLoc = this.state.startLoc;\n    const exprList: N.Expression[] = [];\n    const refExpressionErrors = new ExpressionErrors();\n    let first = true;\n    let spreadStartLoc;\n    let optionalCommaStartLoc;\n\n    while (!this.match(tt.parenR)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(\n          tt.comma,\n          refExpressionErrors.optionalParametersLoc === null\n            ? null\n            : refExpressionErrors.optionalParametersLoc,\n        );\n        if (this.match(tt.parenR)) {\n          optionalCommaStartLoc = this.state.startLoc;\n          break;\n        }\n      }\n\n      if (this.match(tt.ellipsis)) {\n        const spreadNodeStartLoc = this.state.startLoc;\n        spreadStartLoc = this.state.startLoc;\n        exprList.push(\n          this.parseParenItem(this.parseRestBinding(), spreadNodeStartLoc),\n        );\n\n        if (!this.checkCommaAfterRest(charCodes.rightParenthesis)) {\n          break;\n        }\n      } else {\n        exprList.push(\n          this.parseMaybeAssignAllowIn(\n            refExpressionErrors,\n            this.parseParenItem,\n          ),\n        );\n      }\n    }\n\n    const innerEndLoc = this.state.lastTokEndLoc;\n    this.expect(tt.parenR);\n\n    this.state.maybeInArrowParameters = oldMaybeInArrowParameters;\n    this.state.inFSharpPipelineDirectBody = oldInFSharpPipelineDirectBody;\n\n    let arrowNode = this.startNodeAt<N.ArrowFunctionExpression>(startLoc);\n    if (\n      canBeArrow &&\n      this.shouldParseArrow(exprList) &&\n      (arrowNode = this.parseArrow(arrowNode))\n    ) {\n      this.checkDestructuringPrivate(refExpressionErrors);\n      this.expressionScope.validateAsPattern();\n      this.expressionScope.exit();\n      this.parseArrowExpression(arrowNode, exprList, false);\n      // @ts-expect-error todo(flow->ts) improve node types\n      return arrowNode;\n    }\n    this.expressionScope.exit();\n\n    if (!exprList.length) {\n      this.unexpected(this.state.lastTokStartLoc);\n    }\n    if (optionalCommaStartLoc) this.unexpected(optionalCommaStartLoc);\n    if (spreadStartLoc) this.unexpected(spreadStartLoc);\n    this.checkExpressionErrors(refExpressionErrors, true);\n\n    this.toReferencedListDeep(exprList, /* isParenthesizedExpr */ true);\n    if (exprList.length > 1) {\n      val = this.startNodeAt<N.SequenceExpression>(innerStartLoc);\n      val.expressions = exprList;\n      // finish node at current location so it can pick up comments after `)`\n      this.finishNode(val, \"SequenceExpression\");\n      this.resetEndLocation(val, innerEndLoc);\n    } else {\n      val = exprList[0];\n    }\n\n    return this.wrapParenthesis(\n      startLoc,\n      // @ts-expect-error todo(flow->ts)\n      val,\n    );\n  }\n\n  wrapParenthesis(startLoc: Position, expression: N.Expression): N.Expression {\n    if (!this.options.createParenthesizedExpressions) {\n      this.addExtra(expression, \"parenthesized\", true);\n      this.addExtra(expression, \"parenStart\", startLoc.index);\n\n      this.takeSurroundingComments(\n        expression,\n        startLoc.index,\n        this.state.lastTokEndLoc.index,\n      );\n\n      return expression;\n    }\n\n    const parenExpression =\n      this.startNodeAt<N.ParenthesizedExpression>(startLoc);\n    parenExpression.expression = expression;\n    return this.finishNode(parenExpression, \"ParenthesizedExpression\");\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- `params` is used in typescript plugin\n  shouldParseArrow(params: Array<N.Node>): boolean {\n    return !this.canInsertSemicolon();\n  }\n\n  parseArrow(\n    node: Undone<N.ArrowFunctionExpression>,\n  ): Undone<N.ArrowFunctionExpression> | undefined {\n    if (this.eat(tt.arrow)) {\n      return node;\n    }\n  }\n\n  parseParenItem(\n    node: N.Expression,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    startLoc: Position,\n  ): N.Expression {\n    return node;\n  }\n\n  parseNewOrNewTarget(this: Parser): N.NewExpression | N.MetaProperty {\n    const node = this.startNode<N.NewExpression | N.MetaProperty>();\n    this.next();\n    if (this.match(tt.dot)) {\n      // https://tc39.es/ecma262/#prod-NewTarget\n      const meta = this.createIdentifier(\n        this.startNodeAtNode<N.Identifier>(node),\n        \"new\",\n      );\n      this.next();\n      const metaProp = this.parseMetaProperty(\n        node as Undone<N.MetaProperty>,\n        meta,\n        \"target\",\n      );\n\n      if (\n        !this.scope.inNonArrowFunction &&\n        !this.scope.inClass &&\n        !this.options.allowNewTargetOutsideFunction\n      ) {\n        this.raise(Errors.UnexpectedNewTarget, { at: metaProp });\n      }\n\n      return metaProp;\n    }\n\n    return this.parseNew(node as Undone<N.NewExpression>);\n  }\n\n  // New's precedence is slightly tricky. It must allow its argument to\n  // be a `[]` or dot subscript expression, but not a call — at least,\n  // not without wrapping it in parentheses. Thus, it uses the noCalls\n  // argument to parseSubscripts to prevent it from consuming the\n  // argument list.\n  // https://tc39.es/ecma262/#prod-NewExpression\n  parseNew(this: Parser, node: Undone<N.NewExpression>): N.NewExpression {\n    this.parseNewCallee(node);\n\n    if (this.eat(tt.parenL)) {\n      const args = this.parseExprList(tt.parenR);\n      this.toReferencedList(args);\n      // (parseExprList should be all non-null in this case)\n      node.arguments = args;\n    } else {\n      node.arguments = [];\n    }\n\n    return this.finishNode(node, \"NewExpression\");\n  }\n\n  parseNewCallee(this: Parser, node: Undone<N.NewExpression>): void {\n    node.callee = this.parseNoCallExpr();\n    if (node.callee.type === \"Import\") {\n      this.raise(Errors.ImportCallNotNewExpression, { at: node.callee });\n    }\n  }\n\n  // Parse template expression.\n\n  parseTemplateElement(isTagged: boolean): N.TemplateElement {\n    const { start, startLoc, end, value } = this.state;\n    const elemStart = start + 1;\n    const elem = this.startNodeAt<N.TemplateElement>(\n      createPositionWithColumnOffset(startLoc, 1),\n    );\n    if (value === null) {\n      if (!isTagged) {\n        this.raise(Errors.InvalidEscapeSequenceTemplate, {\n          // FIXME: Adding 1 is probably wrong.\n          at: createPositionWithColumnOffset(\n            this.state.firstInvalidTemplateEscapePos,\n            1,\n          ),\n        });\n      }\n    }\n\n    const isTail = this.match(tt.templateTail);\n    const endOffset = isTail ? -1 : -2;\n    const elemEnd = end + endOffset;\n    elem.value = {\n      raw: this.input.slice(elemStart, elemEnd).replace(/\\r\\n?/g, \"\\n\"),\n      cooked: value === null ? null : value.slice(1, endOffset),\n    };\n    elem.tail = isTail;\n    this.next();\n    const finishedNode = this.finishNode(elem, \"TemplateElement\");\n    this.resetEndLocation(\n      finishedNode,\n      createPositionWithColumnOffset(this.state.lastTokEndLoc, endOffset),\n    );\n    return finishedNode;\n  }\n\n  // https://tc39.es/ecma262/#prod-TemplateLiteral\n  parseTemplate(this: Parser, isTagged: boolean): N.TemplateLiteral {\n    const node = this.startNode<N.TemplateLiteral>();\n    node.expressions = [];\n    let curElt = this.parseTemplateElement(isTagged);\n    node.quasis = [curElt];\n    while (!curElt.tail) {\n      node.expressions.push(this.parseTemplateSubstitution());\n      this.readTemplateContinuation();\n      node.quasis.push((curElt = this.parseTemplateElement(isTagged)));\n    }\n    return this.finishNode(node, \"TemplateLiteral\");\n  }\n\n  // This is overwritten by the TypeScript plugin to parse template types\n  parseTemplateSubstitution(this: Parser): N.Expression {\n    return this.parseExpression();\n  }\n\n  // Parse an object literal, binding pattern, or record.\n\n  parseObjectLike(\n    close: TokenType,\n    isPattern: true,\n    isRecord?: boolean | null,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ObjectPattern;\n  parseObjectLike(\n    close: TokenType,\n    isPattern: false,\n    isRecord?: false | null,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ObjectExpression;\n  parseObjectLike(\n    close: TokenType,\n    isPattern: false,\n    isRecord?: true,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.RecordExpression;\n  parseObjectLike<T extends N.ObjectPattern | N.ObjectExpression>(\n    this: Parser,\n    close: TokenType,\n    isPattern: boolean,\n    isRecord?: boolean | null,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): T {\n    if (isRecord) {\n      this.expectPlugin(\"recordAndTuple\");\n    }\n    const oldInFSharpPipelineDirectBody = this.state.inFSharpPipelineDirectBody;\n    this.state.inFSharpPipelineDirectBody = false;\n    const propHash: any = Object.create(null);\n    let first = true;\n    const node = this.startNode<\n      N.ObjectExpression | N.ObjectPattern | N.RecordExpression\n    >();\n\n    node.properties = [];\n    this.next();\n\n    while (!this.match(close)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(tt.comma);\n        if (this.match(close)) {\n          this.addTrailingCommaExtraToNode(\n            // @ts-expect-error todo(flow->ts) improve node types\n            node,\n          );\n          break;\n        }\n      }\n\n      let prop;\n      if (isPattern) {\n        prop = this.parseBindingProperty();\n      } else {\n        prop = this.parsePropertyDefinition(refExpressionErrors);\n        this.checkProto(prop, isRecord, propHash, refExpressionErrors);\n      }\n\n      if (\n        isRecord &&\n        !this.isObjectProperty(prop) &&\n        prop.type !== \"SpreadElement\"\n      ) {\n        this.raise(Errors.InvalidRecordProperty, { at: prop });\n      }\n\n      // @ts-expect-error shorthand may not index prop\n      if (prop.shorthand) {\n        this.addExtra(prop, \"shorthand\", true);\n      }\n\n      // @ts-expect-error Fixme: refine typings\n      node.properties.push(prop);\n    }\n\n    this.next();\n\n    this.state.inFSharpPipelineDirectBody = oldInFSharpPipelineDirectBody;\n    let type = \"ObjectExpression\";\n    if (isPattern) {\n      type = \"ObjectPattern\";\n    } else if (isRecord) {\n      type = \"RecordExpression\";\n    }\n    // @ts-expect-error type is well defined\n    return this.finishNode(node, type);\n  }\n\n  addTrailingCommaExtraToNode(node: N.Node): void {\n    this.addExtra(node, \"trailingComma\", this.state.lastTokStart);\n    this.addExtra(node, \"trailingCommaLoc\", this.state.lastTokStartLoc, false);\n  }\n\n  // Check grammar production:\n  //   IdentifierName *_opt PropertyName\n  // It is used in `parsePropertyDefinition` to detect AsyncMethod and Accessors\n  maybeAsyncOrAccessorProp(prop: Undone<N.ObjectProperty>): boolean {\n    return (\n      !prop.computed &&\n      prop.key.type === \"Identifier\" &&\n      (this.isLiteralPropertyName() ||\n        this.match(tt.bracketL) ||\n        this.match(tt.star))\n    );\n  }\n\n  // https://tc39.es/ecma262/#prod-PropertyDefinition\n  parsePropertyDefinition(\n    this: Parser,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ObjectMember | N.SpreadElement {\n    let decorators = [];\n    if (this.match(tt.at)) {\n      if (this.hasPlugin(\"decorators\")) {\n        this.raise(Errors.UnsupportedPropertyDecorator, {\n          at: this.state.startLoc,\n        });\n      }\n\n      // we needn't check if decorators (stage 0) plugin is enabled since it's checked by\n      // the call to this.parseDecorator\n      while (this.match(tt.at)) {\n        decorators.push(this.parseDecorator());\n      }\n    }\n\n    const prop = this.startNode<N.ObjectProperty>();\n    let isAsync = false;\n    let isAccessor = false;\n    let startLoc;\n\n    if (this.match(tt.ellipsis)) {\n      if (decorators.length) this.unexpected();\n      return this.parseSpread();\n    }\n\n    if (decorators.length) {\n      prop.decorators = decorators;\n      decorators = [];\n    }\n\n    prop.method = false;\n\n    if (refExpressionErrors) {\n      startLoc = this.state.startLoc;\n    }\n\n    let isGenerator = this.eat(tt.star);\n    this.parsePropertyNamePrefixOperator(prop);\n    const containsEsc = this.state.containsEsc;\n    const key = this.parsePropertyName(prop, refExpressionErrors);\n\n    if (!isGenerator && !containsEsc && this.maybeAsyncOrAccessorProp(prop)) {\n      const keyName = key.name;\n      // https://tc39.es/ecma262/#prod-AsyncMethod\n      // https://tc39.es/ecma262/#prod-AsyncGeneratorMethod\n      if (keyName === \"async\" && !this.hasPrecedingLineBreak()) {\n        isAsync = true;\n        this.resetPreviousNodeTrailingComments(key);\n        isGenerator = this.eat(tt.star);\n        this.parsePropertyName(prop);\n      }\n      // get PropertyName[?Yield, ?Await] () { FunctionBody[~Yield, ~Await] }\n      // set PropertyName[?Yield, ?Await] ( PropertySetParameterList ) { FunctionBody[~Yield, ~Await] }\n      if (keyName === \"get\" || keyName === \"set\") {\n        isAccessor = true;\n        this.resetPreviousNodeTrailingComments(key);\n        prop.kind = keyName;\n        if (this.match(tt.star)) {\n          isGenerator = true;\n          this.raise(Errors.AccessorIsGenerator, {\n            at: this.state.curPosition(),\n            kind: keyName,\n          });\n          this.next();\n        }\n        this.parsePropertyName(prop);\n      }\n    }\n\n    return this.parseObjPropValue(\n      prop,\n      startLoc,\n      isGenerator,\n      isAsync,\n      false /* isPattern */,\n      isAccessor,\n      refExpressionErrors,\n    );\n  }\n\n  getGetterSetterExpectedParamCount(\n    method: N.ObjectMethod | N.ClassMethod,\n  ): number {\n    return method.kind === \"get\" ? 0 : 1;\n  }\n\n  // This exists so we can override within the ESTree plugin\n  getObjectOrClassMethodParams(method: N.ObjectMethod | N.ClassMethod) {\n    return method.params;\n  }\n\n  // get methods aren't allowed to have any parameters\n  // set methods must have exactly 1 parameter which is not a rest parameter\n  checkGetterSetterParams(method: N.ObjectMethod | N.ClassMethod): void {\n    const paramCount = this.getGetterSetterExpectedParamCount(method);\n    const params = this.getObjectOrClassMethodParams(method);\n\n    if (params.length !== paramCount) {\n      this.raise(\n        method.kind === \"get\" ? Errors.BadGetterArity : Errors.BadSetterArity,\n        { at: method },\n      );\n    }\n\n    if (\n      method.kind === \"set\" &&\n      params[params.length - 1]?.type === \"RestElement\"\n    ) {\n      this.raise(Errors.BadSetterRestParameter, { at: method });\n    }\n  }\n\n  // https://tc39.es/ecma262/#prod-MethodDefinition\n  parseObjectMethod(\n    this: Parser,\n    prop: Undone<N.ObjectMethod>,\n    isGenerator: boolean,\n    isAsync: boolean,\n    isPattern: boolean,\n    isAccessor: boolean,\n  ): N.ObjectMethod | undefined | null {\n    if (isAccessor) {\n      // isAccessor implies isAsync: false, isPattern: false, isGenerator: false\n      const finishedProp = this.parseMethod(\n        prop,\n        // This _should_ be false, but with error recovery, we allow it to be\n        // set for informational purposes\n        isGenerator,\n        /* isAsync */ false,\n        /* isConstructor */ false,\n        false,\n        \"ObjectMethod\",\n      );\n      this.checkGetterSetterParams(finishedProp);\n      return finishedProp;\n    }\n\n    if (isAsync || isGenerator || this.match(tt.parenL)) {\n      if (isPattern) this.unexpected();\n      prop.kind = \"method\";\n      prop.method = true;\n      return this.parseMethod(\n        prop,\n        isGenerator,\n        isAsync,\n        /* isConstructor */ false,\n        false,\n        \"ObjectMethod\",\n      );\n    }\n  }\n\n  // if `isPattern` is true, parse https://tc39.es/ecma262/#prod-BindingProperty\n  // else https://tc39.es/ecma262/#prod-PropertyDefinition\n  parseObjectProperty(\n    this: Parser,\n    prop: Undone<N.ObjectProperty>,\n    startLoc: Position | undefined | null,\n    isPattern: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ObjectProperty | undefined | null {\n    prop.shorthand = false;\n\n    if (this.eat(tt.colon)) {\n      prop.value = isPattern\n        ? this.parseMaybeDefault(this.state.startLoc)\n        : this.parseMaybeAssignAllowIn(refExpressionErrors);\n\n      return this.finishNode(prop, \"ObjectProperty\");\n    }\n\n    if (!prop.computed && prop.key.type === \"Identifier\") {\n      // PropertyDefinition:\n      //   IdentifierReference\n      //   CoverInitializedName\n      // Note: `{ eval } = {}` will be checked in `checkLVal` later.\n      this.checkReservedWord(prop.key.name, prop.key.loc.start, true, false);\n\n      if (isPattern) {\n        prop.value = this.parseMaybeDefault(\n          startLoc,\n          cloneIdentifier(prop.key),\n        );\n      } else if (this.match(tt.eq)) {\n        const shorthandAssignLoc = this.state.startLoc;\n        if (refExpressionErrors != null) {\n          if (refExpressionErrors.shorthandAssignLoc === null) {\n            refExpressionErrors.shorthandAssignLoc = shorthandAssignLoc;\n          }\n        } else {\n          this.raise(Errors.InvalidCoverInitializedName, {\n            at: shorthandAssignLoc,\n          });\n        }\n        prop.value = this.parseMaybeDefault(\n          startLoc,\n          cloneIdentifier(prop.key),\n        );\n      } else {\n        prop.value = cloneIdentifier(prop.key);\n      }\n      prop.shorthand = true;\n\n      return this.finishNode(prop, \"ObjectProperty\");\n    }\n  }\n\n  parseObjPropValue(\n    this: Parser,\n    prop: Undone<N.ObjectMethod | N.ObjectProperty>,\n    startLoc: Position | undefined | null,\n    isGenerator: boolean,\n    isAsync: boolean,\n    isPattern: boolean,\n    isAccessor: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ObjectMethod | N.ObjectProperty {\n    const node =\n      this.parseObjectMethod(\n        prop as Undone<N.ObjectMethod>,\n        isGenerator,\n        isAsync,\n        isPattern,\n        isAccessor,\n      ) ||\n      this.parseObjectProperty(\n        prop as Undone<N.ObjectProperty>,\n        startLoc,\n        isPattern,\n        refExpressionErrors,\n      );\n\n    if (!node) this.unexpected();\n\n    return node;\n  }\n\n  // https://tc39.es/ecma262/#prod-PropertyName\n  // when refExpressionErrors presents, it will parse private name\n  // and record the position of the first private name\n  parsePropertyName(\n    this: Parser,\n    prop:\n      | Undone<N.ObjectOrClassMember | N.ClassMember>\n      | N.TsNamedTypeElementBase,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.Expression | N.Identifier {\n    if (this.eat(tt.bracketL)) {\n      (prop as Undone<N.ObjectOrClassMember>).computed = true;\n      prop.key = this.parseMaybeAssignAllowIn();\n      this.expect(tt.bracketR);\n    } else {\n      // We check if it's valid for it to be a private name when we push it.\n      const { type, value } = this.state;\n      let key;\n      // most un-computed property names are identifiers\n      if (tokenIsKeywordOrIdentifier(type)) {\n        key = this.parseIdentifier(true);\n      } else {\n        switch (type) {\n          case tt.num:\n            key = this.parseNumericLiteral(value);\n            break;\n          case tt.string:\n            key = this.parseStringLiteral(value);\n            break;\n          case tt.bigint:\n            key = this.parseBigIntLiteral(value);\n            break;\n          case tt.decimal:\n            key = this.parseDecimalLiteral(value);\n            break;\n          case tt.privateName: {\n            // the class private key has been handled in parseClassElementName\n            const privateKeyLoc = this.state.startLoc;\n            if (refExpressionErrors != null) {\n              if (refExpressionErrors.privateKeyLoc === null) {\n                refExpressionErrors.privateKeyLoc = privateKeyLoc;\n              }\n            } else {\n              this.raise(Errors.UnexpectedPrivateField, {\n                at: privateKeyLoc,\n              });\n            }\n            key = this.parsePrivateName();\n            break;\n          }\n          default:\n            this.unexpected();\n        }\n      }\n      (prop as any).key = key;\n      if (type !== tt.privateName) {\n        // ClassPrivateProperty is never computed, so we don't assign in that case.\n        prop.computed = false;\n      }\n    }\n\n    return prop.key;\n  }\n\n  // Initialize empty function node.\n\n  initFunction(node: N.BodilessFunctionOrMethodBase, isAsync: boolean): void {\n    node.id = null;\n    node.generator = false;\n    node.async = isAsync;\n  }\n\n  // Parse object or class method.\n\n  parseMethod<T extends N.ObjectMethod | N.ClassMethod | N.ClassPrivateMethod>(\n    this: Parser,\n    node: Undone<T>,\n    isGenerator: boolean,\n    isAsync: boolean,\n    isConstructor: boolean,\n    allowDirectSuper: boolean,\n    type: T[\"type\"],\n    inClassScope: boolean = false,\n  ): T {\n    this.initFunction(node, isAsync);\n    node.generator = isGenerator;\n    this.scope.enter(\n      SCOPE_FUNCTION |\n        SCOPE_SUPER |\n        (inClassScope ? SCOPE_CLASS : 0) |\n        (allowDirectSuper ? SCOPE_DIRECT_SUPER : 0),\n    );\n    this.prodParam.enter(functionFlags(isAsync, node.generator));\n    this.parseFunctionParams(node, isConstructor);\n    const finishedNode = this.parseFunctionBodyAndFinish(node, type, true);\n    this.prodParam.exit();\n    this.scope.exit();\n\n    return finishedNode;\n  }\n\n  // parse an array literal or tuple literal\n  // https://tc39.es/ecma262/#prod-ArrayLiteral\n  // https://tc39.es/proposal-record-tuple/#prod-TupleLiteral\n  parseArrayLike(\n    this: Parser,\n    close: TokenType,\n    canBePattern: boolean,\n    isTuple: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n  ): N.ArrayExpression | N.TupleExpression {\n    if (isTuple) {\n      this.expectPlugin(\"recordAndTuple\");\n    }\n    const oldInFSharpPipelineDirectBody = this.state.inFSharpPipelineDirectBody;\n    this.state.inFSharpPipelineDirectBody = false;\n    const node = this.startNode<N.ArrayExpression | N.TupleExpression>();\n    this.next();\n    node.elements = this.parseExprList(\n      close,\n      /* allowEmpty */ !isTuple,\n      refExpressionErrors,\n      // @ts-expect-error todo(flow->ts)\n      node,\n    );\n    this.state.inFSharpPipelineDirectBody = oldInFSharpPipelineDirectBody;\n    return this.finishNode(\n      node,\n      isTuple ? \"TupleExpression\" : \"ArrayExpression\",\n    );\n  }\n\n  // Parse arrow function expression.\n  // If the parameters are provided, they will be converted to an\n  // assignable list.\n  parseArrowExpression(\n    this: Parser,\n    node: Undone<N.ArrowFunctionExpression>,\n    params: N.Expression[] | undefined | null,\n    isAsync: boolean,\n    trailingCommaLoc?: Position | null,\n  ): N.ArrowFunctionExpression {\n    this.scope.enter(SCOPE_FUNCTION | SCOPE_ARROW);\n    let flags = functionFlags(isAsync, false);\n    // ConciseBody[In] :\n    //   [lookahead ≠ {] ExpressionBody[?In, ~Await]\n    //   { FunctionBody[~Yield, ~Await] }\n    if (!this.match(tt.braceL) && this.prodParam.hasIn) {\n      flags |= PARAM_IN;\n    }\n    this.prodParam.enter(flags);\n    this.initFunction(node, isAsync);\n    const oldMaybeInArrowParameters = this.state.maybeInArrowParameters;\n\n    if (params) {\n      this.state.maybeInArrowParameters = true;\n      this.setArrowFunctionParameters(node, params, trailingCommaLoc);\n    }\n    this.state.maybeInArrowParameters = false;\n    this.parseFunctionBody(node, true);\n\n    this.prodParam.exit();\n    this.scope.exit();\n    this.state.maybeInArrowParameters = oldMaybeInArrowParameters;\n\n    return this.finishNode(node, \"ArrowFunctionExpression\");\n  }\n\n  setArrowFunctionParameters(\n    node: Undone<N.ArrowFunctionExpression>,\n    params: N.Expression[],\n    trailingCommaLoc?: Position | null,\n  ): void {\n    this.toAssignableList(params, trailingCommaLoc, false);\n    node.params = params as (N.Pattern | N.TSParameterProperty)[];\n  }\n\n  parseFunctionBodyAndFinish<\n    T extends\n      | N.Function\n      | N.TSDeclareMethod\n      | N.TSDeclareFunction\n      | N.ClassPrivateMethod,\n  >(node: Undone<T>, type: T[\"type\"], isMethod: boolean = false): T {\n    // @ts-expect-error (node is not bodiless if we get here)\n    this.parseFunctionBody(node, false, isMethod);\n    return this.finishNode(node, type);\n  }\n\n  // Parse function body and check parameters.\n  parseFunctionBody(\n    this: Parser,\n    node: Undone<N.Function>,\n    allowExpression?: boolean | null,\n    isMethod: boolean = false,\n  ): void {\n    const isExpression = allowExpression && !this.match(tt.braceL);\n    this.expressionScope.enter(newExpressionScope());\n\n    if (isExpression) {\n      // https://tc39.es/ecma262/#prod-ExpressionBody\n      (node as Undone<N.ArrowFunctionExpression>).body =\n        this.parseMaybeAssign();\n      this.checkParams(node, false, allowExpression, false);\n    } else {\n      const oldStrict = this.state.strict;\n      // Start a new scope with regard to labels\n      // flag (restore them to their old value afterwards).\n      const oldLabels = this.state.labels;\n      this.state.labels = [];\n\n      // FunctionBody[Yield, Await]:\n      //   StatementList[?Yield, ?Await, +Return] opt\n      this.prodParam.enter(this.prodParam.currentFlags() | PARAM_RETURN);\n      node.body = this.parseBlock(\n        true,\n        false,\n        // Strict mode function checks after we parse the statements in the function body.\n        (hasStrictModeDirective: boolean) => {\n          const nonSimple = !this.isSimpleParamList(node.params);\n\n          if (hasStrictModeDirective && nonSimple) {\n            // This logic is here to align the error location with the ESTree plugin.\n            this.raise(Errors.IllegalLanguageModeDirective, {\n              at:\n                // @ts-expect-error kind may not index node\n                (node.kind === \"method\" || node.kind === \"constructor\") &&\n                // @ts-expect-error key may not index node\n                !!node.key\n                  ? // @ts-expect-error node.key has been guarded\n                    node.key.loc.end\n                  : node,\n            });\n          }\n\n          const strictModeChanged = !oldStrict && this.state.strict;\n\n          // Add the params to varDeclaredNames to ensure that an error is thrown\n          // if a let/const declaration in the function clashes with one of the params.\n          this.checkParams(\n            node,\n            !this.state.strict && !allowExpression && !isMethod && !nonSimple,\n            allowExpression,\n            strictModeChanged,\n          );\n\n          // Ensure the function name isn't a forbidden identifier in strict mode, e.g. 'eval'\n          if (this.state.strict && node.id) {\n            this.checkIdentifier(node.id, BIND_OUTSIDE, strictModeChanged);\n          }\n        },\n      );\n      this.prodParam.exit();\n      this.state.labels = oldLabels;\n    }\n    this.expressionScope.exit();\n  }\n\n  isSimpleParameter(node: N.Pattern | N.TSParameterProperty) {\n    return node.type === \"Identifier\";\n  }\n\n  isSimpleParamList(\n    params: ReadonlyArray<N.Pattern | N.TSParameterProperty>,\n  ): boolean {\n    for (let i = 0, len = params.length; i < len; i++) {\n      if (!this.isSimpleParameter(params[i])) return false;\n    }\n    return true;\n  }\n\n  checkParams(\n    node: Undone<N.Function>,\n    allowDuplicates: boolean,\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    isArrowFunction?: boolean | null,\n    strictModeChanged: boolean = true,\n  ): void {\n    const checkClashes = !allowDuplicates && new Set<string>();\n    // We create a fake node with the \"ephemeral\" type `FormalParameters`[1]\n    // since we just store an array of parameters. Perhaps someday we can have\n    // something like class FormalParameters extends Array { ... }, which would\n    // also be helpful when traversing this node.\n    //\n    // 1. https://tc39.es/ecma262/#prod-FormalParameters\n    const formalParameters = { type: \"FormalParameters\" } as const;\n    for (const param of node.params) {\n      this.checkLVal(param, {\n        in: formalParameters,\n        binding: BIND_VAR,\n        checkClashes,\n        strictModeChanged,\n      });\n    }\n  }\n\n  // Parses a comma-separated list of expressions, and returns them as\n  // an array. `close` is the token type that ends the list, and\n  // `allowEmpty` can be turned on to allow subsequent commas with\n  // nothing in between them to be parsed as `null` (which is needed\n  // for array literals).\n\n  parseExprList(\n    this: Parser,\n    close: TokenType,\n    allowEmpty?: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n    nodeForExtra?: N.Node | null,\n  ): (N.Expression | null)[] {\n    const elts: (N.Expression | null)[] = [];\n    let first = true;\n\n    while (!this.eat(close)) {\n      if (first) {\n        first = false;\n      } else {\n        this.expect(tt.comma);\n        if (this.match(close)) {\n          if (nodeForExtra) {\n            this.addTrailingCommaExtraToNode(nodeForExtra);\n          }\n          this.next();\n          break;\n        }\n      }\n\n      elts.push(this.parseExprListItem(allowEmpty, refExpressionErrors));\n    }\n    return elts;\n  }\n\n  parseExprListItem(\n    this: Parser,\n    allowEmpty?: boolean,\n    refExpressionErrors?: ExpressionErrors | null,\n    allowPlaceholder?: boolean | null,\n  ): N.Expression | null;\n  parseExprListItem(\n    this: Parser,\n    allowEmpty?: false,\n    refExpressionErrors?: ExpressionErrors | null,\n    allowPlaceholder?: boolean | null,\n  ): N.Expression;\n  parseExprListItem(\n    this: Parser,\n    allowEmpty?: boolean | null,\n    refExpressionErrors?: ExpressionErrors | null,\n    allowPlaceholder?: boolean | null,\n  ): N.Expression | null {\n    let elt;\n    if (this.match(tt.comma)) {\n      if (!allowEmpty) {\n        this.raise(Errors.UnexpectedToken, {\n          at: this.state.curPosition(),\n          unexpected: \",\",\n        });\n      }\n      elt = null;\n    } else if (this.match(tt.ellipsis)) {\n      const spreadNodeStartLoc = this.state.startLoc;\n\n      elt = this.parseParenItem(\n        this.parseSpread(refExpressionErrors),\n        spreadNodeStartLoc,\n      );\n    } else if (this.match(tt.question)) {\n      this.expectPlugin(\"partialApplication\");\n      if (!allowPlaceholder) {\n        this.raise(Errors.UnexpectedArgumentPlaceholder, {\n          at: this.state.startLoc,\n        });\n      }\n      const node = this.startNode();\n      this.next();\n      elt = this.finishNode(node, \"ArgumentPlaceholder\");\n    } else {\n      elt = this.parseMaybeAssignAllowIn(\n        refExpressionErrors,\n        this.parseParenItem,\n      );\n    }\n    return elt;\n  }\n\n  // Parse the next token as an identifier. If `liberal` is true (used\n  // when parsing properties), it will also convert keywords into\n  // identifiers.\n  // This shouldn't be used to parse the keywords of meta properties, since they\n  // are not identifiers and cannot contain escape sequences.\n\n  parseIdentifier(liberal?: boolean): N.Identifier {\n    const node = this.startNode<N.Identifier>();\n    const name = this.parseIdentifierName(liberal);\n\n    return this.createIdentifier(node, name);\n  }\n\n  createIdentifier(\n    node: Omit<N.Identifier, \"type\">,\n    name: string,\n  ): N.Identifier {\n    node.name = name;\n    node.loc.identifierName = name;\n\n    return this.finishNode(node, \"Identifier\");\n  }\n\n  parseIdentifierName(liberal?: boolean): string {\n    let name: string;\n\n    const { startLoc, type } = this.state;\n\n    if (tokenIsKeywordOrIdentifier(type)) {\n      name = this.state.value;\n    } else {\n      this.unexpected();\n    }\n\n    const tokenIsKeyword = tokenKeywordOrIdentifierIsKeyword(type);\n\n    if (liberal) {\n      // If the current token is not used as a keyword, set its type to \"tt.name\".\n      // This will prevent this.next() from throwing about unexpected escapes.\n      if (tokenIsKeyword) {\n        this.replaceToken(tt.name);\n      }\n    } else {\n      this.checkReservedWord(name, startLoc, tokenIsKeyword, false);\n    }\n\n    this.next();\n\n    return name;\n  }\n\n  checkReservedWord(\n    word: string,\n    startLoc: Position,\n    checkKeywords: boolean,\n    isBinding: boolean,\n  ): void {\n    // Every JavaScript reserved word is 10 characters or less.\n    if (word.length > 10) {\n      return;\n    }\n    // Most identifiers are not reservedWord-like, they don't need special\n    // treatments afterward, which very likely ends up throwing errors\n    if (!canBeReservedWord(word)) {\n      return;\n    }\n\n    if (checkKeywords && isKeyword(word)) {\n      this.raise(Errors.UnexpectedKeyword, {\n        at: startLoc,\n        keyword: word,\n      });\n      return;\n    }\n\n    const reservedTest = !this.state.strict\n      ? isReservedWord\n      : isBinding\n      ? isStrictBindReservedWord\n      : isStrictReservedWord;\n\n    if (reservedTest(word, this.inModule)) {\n      this.raise(Errors.UnexpectedReservedWord, {\n        at: startLoc,\n        reservedWord: word,\n      });\n      return;\n    } else if (word === \"yield\") {\n      if (this.prodParam.hasYield) {\n        this.raise(Errors.YieldBindingIdentifier, { at: startLoc });\n        return;\n      }\n    } else if (word === \"await\") {\n      if (this.prodParam.hasAwait) {\n        this.raise(Errors.AwaitBindingIdentifier, { at: startLoc });\n        return;\n      }\n\n      if (this.scope.inStaticBlock) {\n        this.raise(Errors.AwaitBindingIdentifierInStaticBlock, {\n          at: startLoc,\n        });\n        return;\n      }\n\n      this.expressionScope.recordAsyncArrowParametersError({ at: startLoc });\n    } else if (word === \"arguments\") {\n      if (this.scope.inClassAndNotInNonArrowFunction) {\n        this.raise(Errors.ArgumentsInClass, { at: startLoc });\n        return;\n      }\n    }\n  }\n\n  isAwaitAllowed(): boolean {\n    if (this.prodParam.hasAwait) return true;\n    if (this.options.allowAwaitOutsideFunction && !this.scope.inFunction) {\n      return true;\n    }\n    return false;\n  }\n\n  // Parses await expression inside async function.\n\n  parseAwait(this: Parser, startLoc: Position): N.AwaitExpression {\n    const node = this.startNodeAt<N.AwaitExpression>(startLoc);\n\n    this.expressionScope.recordParameterInitializerError(\n      Errors.AwaitExpressionFormalParameter,\n      {\n        // @ts-expect-error todo(flow->ts)\n        at: node,\n      },\n    );\n\n    if (this.eat(tt.star)) {\n      this.raise(Errors.ObsoleteAwaitStar, { at: node });\n    }\n\n    if (!this.scope.inFunction && !this.options.allowAwaitOutsideFunction) {\n      if (this.isAmbiguousAwait()) {\n        this.ambiguousScriptDifferentAst = true;\n      } else {\n        this.sawUnambiguousESM = true;\n      }\n    }\n\n    if (!this.state.soloAwait) {\n      node.argument = this.parseMaybeUnary(null, true);\n    }\n\n    return this.finishNode(node, \"AwaitExpression\");\n  }\n\n  isAmbiguousAwait(): boolean {\n    if (this.hasPrecedingLineBreak()) return true;\n    const { type } = this.state;\n    return (\n      // All the following expressions are ambiguous:\n      //   await + 0, await - 0, await ( 0 ), await [ 0 ], await / 0 /u, await ``, await of []\n      type === tt.plusMin ||\n      type === tt.parenL ||\n      type === tt.bracketL ||\n      tokenIsTemplate(type) ||\n      (type === tt._of && !this.state.containsEsc) ||\n      // Sometimes the tokenizer generates tt.slash for regexps, and this is\n      // handler by parseExprAtom\n      type === tt.regexp ||\n      type === tt.slash ||\n      // This code could be parsed both as a modulo operator or as an intrinsic:\n      //   await %x(0)\n      (this.hasPlugin(\"v8intrinsic\") && type === tt.modulo)\n    );\n  }\n\n  // Parses yield expression inside generator.\n\n  parseYield(this: Parser): N.YieldExpression {\n    const node = this.startNode<N.YieldExpression>();\n\n    this.expressionScope.recordParameterInitializerError(\n      Errors.YieldInParameter,\n      {\n        // @ts-expect-error todo(flow->ts)\n        at: node,\n      },\n    );\n\n    this.next();\n    let delegating = false;\n    let argument: N.Expression | null = null;\n    if (!this.hasPrecedingLineBreak()) {\n      delegating = this.eat(tt.star);\n      switch (this.state.type) {\n        case tt.semi:\n        case tt.eof:\n        case tt.braceR:\n        case tt.parenR:\n        case tt.bracketR:\n        case tt.braceBarR:\n        case tt.colon:\n        case tt.comma:\n          // The above is the complete set of tokens that can\n          // follow an AssignmentExpression, and none of them\n          // can start an AssignmentExpression\n          if (!delegating) break;\n        /* fallthrough */\n        default:\n          argument = this.parseMaybeAssign();\n      }\n    }\n    node.delegate = delegating;\n    node.argument = argument;\n    return this.finishNode(node, \"YieldExpression\");\n  }\n\n  // Validates a pipeline (for any of the pipeline Babylon plugins) at the point\n  // of the infix operator `|>`.\n\n  checkPipelineAtInfixOperator(left: N.Expression, leftStartLoc: Position) {\n    if (this.hasPlugin([\"pipelineOperator\", { proposal: \"smart\" }])) {\n      if (left.type === \"SequenceExpression\") {\n        // Ensure that the pipeline head is not a comma-delimited\n        // sequence expression.\n        this.raise(Errors.PipelineHeadSequenceExpression, {\n          at: leftStartLoc,\n        });\n      }\n    }\n  }\n\n  parseSmartPipelineBodyInStyle(childExpr: N.Expression, startLoc: Position) {\n    if (this.isSimpleReference(childExpr)) {\n      const bodyNode = this.startNodeAt<N.PipelineBareFunction>(startLoc);\n      bodyNode.callee = childExpr;\n      return this.finishNode(bodyNode, \"PipelineBareFunction\");\n    } else {\n      const bodyNode = this.startNodeAt<N.PipelineTopicExpression>(startLoc);\n      this.checkSmartPipeTopicBodyEarlyErrors(startLoc);\n      bodyNode.expression = childExpr;\n      return this.finishNode(bodyNode, \"PipelineTopicExpression\");\n    }\n  }\n\n  isSimpleReference(expression: N.Expression): boolean {\n    switch (expression.type) {\n      case \"MemberExpression\":\n        return (\n          !expression.computed && this.isSimpleReference(expression.object)\n        );\n      case \"Identifier\":\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  // This helper method is to be called immediately\n  // after a topic-style smart-mix pipe body is parsed.\n  // The `startLoc` is the starting position of the pipe body.\n\n  checkSmartPipeTopicBodyEarlyErrors(startLoc: Position): void {\n    // If the following token is invalidly `=>`, then throw a human-friendly error\n    // instead of something like 'Unexpected token, expected \";\"'.\n    // For example, `x => x |> y => #` (assuming `#` is the topic reference)\n    // groups into `x => (x |> y) => #`,\n    // and `(x |> y) => #` is an invalid arrow function.\n    // This is because smart-mix `|>` has tighter precedence than `=>`.\n    if (this.match(tt.arrow)) {\n      throw this.raise(Errors.PipelineBodyNoArrow, { at: this.state.startLoc });\n    }\n\n    // A topic-style smart-mix pipe body must use the topic reference at least once.\n    if (!this.topicReferenceWasUsedInCurrentContext()) {\n      this.raise(Errors.PipelineTopicUnused, { at: startLoc });\n    }\n  }\n\n  // Enable topic references from outer contexts within Hack-style pipe bodies.\n  // The function modifies the parser's topic-context state to enable or disable\n  // the use of topic references.\n  // The function then calls a callback, then resets the parser\n  // to the old topic-context state that it had before the function was called.\n\n  withTopicBindingContext<T>(callback: () => T): T {\n    const outerContextTopicState = this.state.topicContext;\n    this.state.topicContext = {\n      // Enable the use of the primary topic reference.\n      maxNumOfResolvableTopics: 1,\n      // Hide the use of any topic references from outer contexts.\n      maxTopicIndex: null,\n    };\n\n    try {\n      return callback();\n    } finally {\n      this.state.topicContext = outerContextTopicState;\n    }\n  }\n\n  // This helper method is used only with the deprecated smart-mix pipe proposal.\n  // Disables topic references from outer contexts within syntax constructs\n  // such as the bodies of iteration statements.\n  // The function modifies the parser's topic-context state to enable or disable\n  // the use of topic references with the smartPipelines plugin. They then run a\n  // callback, then they reset the parser to the old topic-context state that it\n  // had before the function was called.\n\n  withSmartMixTopicForbiddingContext<T>(callback: () => T): T {\n    if (this.hasPlugin([\"pipelineOperator\", { proposal: \"smart\" }])) {\n      // Reset the parser’s topic context only if the smart-mix pipe proposal is active.\n      const outerContextTopicState = this.state.topicContext;\n      this.state.topicContext = {\n        // Disable the use of the primary topic reference.\n        maxNumOfResolvableTopics: 0,\n        // Hide the use of any topic references from outer contexts.\n        maxTopicIndex: null,\n      };\n\n      try {\n        return callback();\n      } finally {\n        this.state.topicContext = outerContextTopicState;\n      }\n    } else {\n      // If the pipe proposal is \"minimal\", \"fsharp\", or \"hack\",\n      // or if no pipe proposal is active,\n      // then the callback result is returned\n      // without touching any extra parser state.\n      return callback();\n    }\n  }\n\n  withSoloAwaitPermittingContext<T>(callback: () => T): T {\n    const outerContextSoloAwaitState = this.state.soloAwait;\n    this.state.soloAwait = true;\n\n    try {\n      return callback();\n    } finally {\n      this.state.soloAwait = outerContextSoloAwaitState;\n    }\n  }\n\n  allowInAnd<T>(callback: () => T): T {\n    const flags = this.prodParam.currentFlags();\n    const prodParamToSet = PARAM_IN & ~flags;\n    if (prodParamToSet) {\n      this.prodParam.enter(flags | PARAM_IN);\n      try {\n        return callback();\n      } finally {\n        this.prodParam.exit();\n      }\n    }\n    return callback();\n  }\n\n  disallowInAnd<T>(callback: () => T): T {\n    const flags = this.prodParam.currentFlags();\n    const prodParamToClear = PARAM_IN & flags;\n    if (prodParamToClear) {\n      this.prodParam.enter(flags & ~PARAM_IN);\n      try {\n        return callback();\n      } finally {\n        this.prodParam.exit();\n      }\n    }\n    return callback();\n  }\n\n  // Register the use of a topic reference within the current\n  // topic-binding context.\n  registerTopicReference(): void {\n    this.state.topicContext.maxTopicIndex = 0;\n  }\n\n  topicReferenceIsAllowedInCurrentContext(): boolean {\n    return this.state.topicContext.maxNumOfResolvableTopics >= 1;\n  }\n\n  topicReferenceWasUsedInCurrentContext(): boolean {\n    return (\n      this.state.topicContext.maxTopicIndex != null &&\n      this.state.topicContext.maxTopicIndex >= 0\n    );\n  }\n\n  parseFSharpPipelineBody(this: Parser, prec: number): N.Expression {\n    const startLoc = this.state.startLoc;\n\n    this.state.potentialArrowAt = this.state.start;\n    const oldInFSharpPipelineDirectBody = this.state.inFSharpPipelineDirectBody;\n    this.state.inFSharpPipelineDirectBody = true;\n\n    const ret = this.parseExprOp(\n      this.parseMaybeUnaryOrPrivate(),\n      startLoc,\n      prec,\n    );\n\n    this.state.inFSharpPipelineDirectBody = oldInFSharpPipelineDirectBody;\n\n    return ret;\n  }\n\n  // https://github.com/tc39/proposal-js-module-blocks\n  parseModuleExpression(this: Parser): N.ModuleExpression {\n    this.expectPlugin(\"moduleBlocks\");\n    const node = this.startNode<N.ModuleExpression>();\n    this.next(); // eat \"module\"\n    if (!this.match(tt.braceL)) {\n      this.unexpected(null, tt.braceL);\n    }\n    // start program node immediately after `{`\n    const program = this.startNodeAt<N.Program>(this.state.endLoc);\n    this.next(); // eat `{`\n\n    const revertScopes = this.initializeScopes(/** inModule */ true);\n    this.enterInitialScopes();\n\n    try {\n      node.body = this.parseProgram(program, tt.braceR, \"module\");\n    } finally {\n      revertScopes();\n    }\n    return this.finishNode<N.ModuleExpression>(node, \"ModuleExpression\");\n  }\n\n  // Used in Flow plugin\n  parsePropertyNamePrefixOperator(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    prop: Undone<N.ObjectOrClassMember | N.ClassMember>,\n  ): void {}\n}\n"], "mappings": ";;;;;;AAkBA,IAAAA,MAAA,GAAAC,OAAA;AAiBA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AAQA,IAAAG,SAAA,GAAAH,OAAA;AAKA,IAAAI,WAAA,GAAAJ,OAAA;AASA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,oBAAA,GAAAN,OAAA;AAMA,IAAAO,gBAAA,GAAAP,OAAA;AAKA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,uBAAA,GAAAT,OAAA;AACA,IAAAU,SAAA,GAAAV,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AAKe,MAAeY,gBAAgB,SAASC,aAAU,CAAC;EAuChEC,UAAUA,CACRC,IAAsC,EACtCC,QAAoC,EACpCC,QAEC,EACDC,mBAA6C,EACvC;IACN,IACEH,IAAI,CAACI,IAAI,KAAK,eAAe,IAC7B,IAAI,CAACC,cAAc,CAACL,IAAI,CAAC,IACzBA,IAAI,CAACM,QAAQ,IAEbN,IAAI,CAACO,SAAS,EACd;MACA;IACF;IAEA,MAAMC,GAAG,GAAGR,IAAI,CAACQ,GAAG;IAEpB,MAAMC,IAAI,GAAGD,GAAG,CAACJ,IAAI,KAAK,YAAY,GAAGI,GAAG,CAACC,IAAI,GAAGD,GAAG,CAACE,KAAK;IAE7D,IAAID,IAAI,KAAK,WAAW,EAAE;MACxB,IAAIR,QAAQ,EAAE;QACZ,IAAI,CAACU,KAAK,CAACC,kBAAM,CAACC,aAAa,EAAE;UAAEC,EAAE,EAAEN;QAAI,CAAC,CAAC;QAC7C;MACF;MACA,IAAIN,QAAQ,CAACa,IAAI,EAAE;QACjB,IAAIZ,mBAAmB,EAAE;UAGvB,IAAIA,mBAAmB,CAACa,cAAc,KAAK,IAAI,EAAE;YAC/Cb,mBAAmB,CAACa,cAAc,GAAGR,GAAG,CAACS,GAAG,CAACC,KAAK;UACpD;QACF,CAAC,MAAM;UACL,IAAI,CAACP,KAAK,CAACC,kBAAM,CAACO,cAAc,EAAE;YAAEL,EAAE,EAAEN;UAAI,CAAC,CAAC;QAChD;MACF;MAEAN,QAAQ,CAACa,IAAI,GAAG,IAAI;IACtB;EACF;EAEAK,oBAAoBA,CAACC,IAAkB,EAAEC,gBAAwB,EAAW;IAC1E,OACED,IAAI,CAACjB,IAAI,KAAK,yBAAyB,IAAIiB,IAAI,CAACH,KAAK,KAAKI,gBAAgB;EAE9E;EAGAC,aAAaA,CAAA,EAA8C;IACzD,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,MAAMJ,IAAI,GAAG,IAAI,CAACK,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACC,KAAK,IAAO,CAAC,EAAE;MACvB,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;IAGA,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChCR,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;IACnCT,IAAI,CAACW,MAAM,GAAG,IAAI,CAACD,KAAK,CAACC,MAAM;IAC/B,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAE;MACvBb,IAAI,CAACa,MAAM,GAAG,IAAI,CAACA,MAAM;IAC3B;IAEA,OAAOb,IAAI;EACb;EAqBAK,eAAeA,CAEbS,UAAoB,EACpBhC,mBAAsC,EACxB;IACd,IAAIgC,UAAU,EAAE;MACd,OAAO,IAAI,CAACC,aAAa,CAAC,MACxB,IAAI,CAACC,mBAAmB,CAAClC,mBAAmB,CAC9C,CAAC;IACH;IACA,OAAO,IAAI,CAACmC,UAAU,CAAC,MAAM,IAAI,CAACD,mBAAmB,CAAClC,mBAAmB,CAAC,CAAC;EAC7E;EAGAkC,mBAAmBA,CAEjBlC,mBAAsC,EACxB;IACd,MAAMoC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMlB,IAAI,GAAG,IAAI,CAACmB,gBAAgB,CAACrC,mBAAmB,CAAC;IACvD,IAAI,IAAI,CAACwB,KAAK,GAAS,CAAC,EAAE;MACxB,MAAMc,IAAI,GAAG,IAAI,CAACC,WAAW,CAACH,QAAQ,CAAC;MACvCE,IAAI,CAACE,WAAW,GAAG,CAACtB,IAAI,CAAC;MACzB,OAAO,IAAI,CAACuB,GAAG,GAAS,CAAC,EAAE;QACzBH,IAAI,CAACE,WAAW,CAACE,IAAI,CAAC,IAAI,CAACL,gBAAgB,CAACrC,mBAAmB,CAAC,CAAC;MACnE;MACA,IAAI,CAAC2C,gBAAgB,CAACL,IAAI,CAACE,WAAW,CAAC;MACvC,OAAO,IAAI,CAACI,UAAU,CAACN,IAAI,EAAE,oBAAoB,CAAC;IACpD;IACA,OAAOpB,IAAI;EACb;EAGA2B,0BAA0BA,CAExB7C,mBAA6C,EAC7C8C,cAAyB,EACzB;IACA,OAAO,IAAI,CAACb,aAAa,CAAC,MACxB,IAAI,CAACI,gBAAgB,CAACrC,mBAAmB,EAAE8C,cAAc,CAC3D,CAAC;EACH;EAGAC,uBAAuBA,CAErB/C,mBAA6C,EAC7C8C,cAAyB,EACzB;IACA,OAAO,IAAI,CAACX,UAAU,CAAC,MACrB,IAAI,CAACE,gBAAgB,CAACrC,mBAAmB,EAAE8C,cAAc,CAC3D,CAAC;EACH;EAIAE,0BAA0BA,CACxBhD,mBAAqC,EACrCiD,WAA6B,EAC7B;IAAA,IAAAC,gBAAA;IACAlD,mBAAmB,CAACmD,qBAAqB,IAAAD,gBAAA,GACvCD,WAAW,oBAAXA,WAAW,CAAEnC,GAAG,YAAAoC,gBAAA,GAAI,IAAI,CAACtB,KAAK,CAACQ,QAAQ;EAC3C;EAKAC,gBAAgBA,CAEdrC,mBAA6C,EAC7C8C,cAAyB,EACX;IACd,MAAMV,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,IAAI,IAAI,CAACgB,YAAY,IAAU,CAAC,EAAE;MAChC,IAAI,IAAI,CAACC,SAAS,CAACC,QAAQ,EAAE;QAC3B,IAAIC,IAAI,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAC5B,IAAIV,cAAc,EAAE;UAClBS,IAAI,GAAGT,cAAc,CAACW,IAAI,CAAC,IAAI,EAAEF,IAAI,EAAEnB,QAAQ,CAAC;QAClD;QACA,OAAOmB,IAAI;MACb;IACF;IAEA,IAAIG,mBAAmB;IACvB,IAAI1D,mBAAmB,EAAE;MACvB0D,mBAAmB,GAAG,KAAK;IAC7B,CAAC,MAAM;MACL1D,mBAAmB,GAAG,IAAI2D,sBAAgB,CAAC,CAAC;MAC5CD,mBAAmB,GAAG,IAAI;IAC5B;IACA,MAAM;MAAEzD;IAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAE3B,IAAI3B,IAAI,OAAc,IAAI,IAAA2D,wBAAiB,EAAC3D,IAAI,CAAC,EAAE;MACjD,IAAI,CAAC2B,KAAK,CAACT,gBAAgB,GAAG,IAAI,CAACS,KAAK,CAACb,KAAK;IAChD;IAEA,IAAIwC,IAAI,GAAG,IAAI,CAACM,qBAAqB,CAAC7D,mBAAmB,CAAC;IAC1D,IAAI8C,cAAc,EAAE;MAClBS,IAAI,GAAGT,cAAc,CAACW,IAAI,CAAC,IAAI,EAAEF,IAAI,EAAEnB,QAAQ,CAAC;IAClD;IACA,IAAI,IAAA0B,wBAAiB,EAAC,IAAI,CAAClC,KAAK,CAAC3B,IAAI,CAAC,EAAE;MACtC,MAAMqC,IAAI,GAAG,IAAI,CAACC,WAAW,CAAyBH,QAAQ,CAAC;MAC/D,MAAM2B,QAAQ,GAAG,IAAI,CAACnC,KAAK,CAACrB,KAAK;MACjC+B,IAAI,CAACyB,QAAQ,GAAGA,QAAQ;MAExB,IAAI,IAAI,CAACvC,KAAK,GAAM,CAAC,EAAE;QACrB,IAAI,CAACwC,YAAY,CAACT,IAAI,EAAc,IAAI,CAAC;QACzCjB,IAAI,CAACiB,IAAI,GAAGA,IAAI;QAEhB,MAAMU,UAAU,GAAG7B,QAAQ,CAAC8B,KAAK;QACjC,IACElE,mBAAmB,CAACa,cAAc,IAAI,IAAI,IAC1Cb,mBAAmB,CAACa,cAAc,CAACqD,KAAK,IAAID,UAAU,EACtD;UACAjE,mBAAmB,CAACa,cAAc,GAAG,IAAI;QAC3C;QACA,IACEb,mBAAmB,CAACmE,kBAAkB,IAAI,IAAI,IAC9CnE,mBAAmB,CAACmE,kBAAkB,CAACD,KAAK,IAAID,UAAU,EAC1D;UACAjE,mBAAmB,CAACmE,kBAAkB,GAAG,IAAI;QAC/C;QACA,IACEnE,mBAAmB,CAACoE,aAAa,IAAI,IAAI,IACzCpE,mBAAmB,CAACoE,aAAa,CAACF,KAAK,IAAID,UAAU,EACrD;UACA,IAAI,CAACI,yBAAyB,CAACrE,mBAAmB,CAAC;UACnDA,mBAAmB,CAACoE,aAAa,GAAG,IAAI;QAC1C;MACF,CAAC,MAAM;QACL9B,IAAI,CAACiB,IAAI,GAAGA,IAAI;MAClB;MAEA,IAAI,CAACe,IAAI,CAAC,CAAC;MACXhC,IAAI,CAACiC,KAAK,GAAG,IAAI,CAAClC,gBAAgB,CAAC,CAAC;MACpC,IAAI,CAACmC,SAAS,CAACjB,IAAI,EAAE;QACnBkB,EAAE,EAAE,IAAI,CAAC7B,UAAU,CAACN,IAAI,EAAE,sBAAsB;MAClD,CAAC,CAAC;MAEF,OAAOA,IAAI;IACb,CAAC,MAAM,IAAIoB,mBAAmB,EAAE;MAC9B,IAAI,CAACgB,qBAAqB,CAAC1E,mBAAmB,EAAE,IAAI,CAAC;IACvD;IAEA,OAAOuD,IAAI;EACb;EAKAM,qBAAqBA,CAEnB7D,mBAAqC,EACvB;IACd,MAAMoC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMjB,gBAAgB,GAAG,IAAI,CAACS,KAAK,CAACT,gBAAgB;IACpD,MAAMD,IAAI,GAAG,IAAI,CAACyD,YAAY,CAAC3E,mBAAmB,CAAC;IAEnD,IAAI,IAAI,CAACiB,oBAAoB,CAACC,IAAI,EAAEC,gBAAgB,CAAC,EAAE;MACrD,OAAOD,IAAI;IACb;IAEA,OAAO,IAAI,CAAC0D,gBAAgB,CAAC1D,IAAI,EAAEkB,QAAQ,EAAEpC,mBAAmB,CAAC;EACnE;EAEA4E,gBAAgBA,CAEd1D,IAAkB,EAClBkB,QAAkB,EAElBpC,mBAA6C,EAC/B;IACd,IAAI,IAAI,CAACyC,GAAG,GAAY,CAAC,EAAE;MACzB,MAAMH,IAAI,GAAG,IAAI,CAACC,WAAW,CAACH,QAAQ,CAAC;MACvCE,IAAI,CAACuC,IAAI,GAAG3D,IAAI;MAChBoB,IAAI,CAACwC,UAAU,GAAG,IAAI,CAAC/B,uBAAuB,CAAC,CAAC;MAChD,IAAI,CAACgC,MAAM,GAAS,CAAC;MACrBzC,IAAI,CAAC0C,SAAS,GAAG,IAAI,CAAC3C,gBAAgB,CAAC,CAAC;MACxC,OAAO,IAAI,CAACO,UAAU,CAACN,IAAI,EAAE,uBAAuB,CAAC;IACvD;IACA,OAAOpB,IAAI;EACb;EAEA+D,wBAAwBA,CAEtBjF,mBAAsC,EACR;IAC9B,OAAO,IAAI,CAACwB,KAAK,IAAe,CAAC,GAC7B,IAAI,CAAC0D,gBAAgB,CAAC,CAAC,GACvB,IAAI,CAACC,eAAe,CAACnF,mBAAmB,CAAC;EAC/C;EAKA2E,YAAYA,CAEV3E,mBAAqC,EACvB;IACd,MAAMoC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMjB,gBAAgB,GAAG,IAAI,CAACS,KAAK,CAACT,gBAAgB;IACpD,MAAMD,IAAI,GAAG,IAAI,CAAC+D,wBAAwB,CAACjF,mBAAmB,CAAC;IAE/D,IAAI,IAAI,CAACiB,oBAAoB,CAACC,IAAI,EAAEC,gBAAgB,CAAC,EAAE;MACrD,OAAOD,IAAI;IACb;IAEA,OAAO,IAAI,CAACkE,WAAW,CAAClE,IAAI,EAAEkB,QAAQ,EAAE,CAAC,CAAC,CAAC;EAC7C;EAQAgD,WAAWA,CAET7B,IAAkC,EAClC8B,YAAsB,EACtBC,OAAe,EACD;IACd,IAAI,IAAI,CAACC,aAAa,CAAChC,IAAI,CAAC,EAAE;MAK5B,MAAMhD,KAAK,GAAG,IAAI,CAACiF,gBAAgB,CAACjC,IAAI,CAAC;MAEzC,IACE+B,OAAO,IAAI,IAAAG,8BAAuB,IAAO,CAAC,IAC1C,CAAC,IAAI,CAACpC,SAAS,CAACqC,KAAK,IACrB,CAAC,IAAI,CAAClE,KAAK,GAAO,CAAC,EACnB;QACA,IAAI,CAAChB,KAAK,CAACC,kBAAM,CAACkF,mBAAmB,EAAE;UACrChF,EAAE,EAAE4C,IAAI;UACRqC,cAAc,EAAErF;QAClB,CAAC,CAAC;MACJ;MAEA,IAAI,CAACsF,UAAU,CAACC,cAAc,CAACvF,KAAK,EAAEgD,IAAI,CAACzC,GAAG,CAACC,KAAK,CAAC;IACvD;IAEA,MAAMgF,EAAE,GAAG,IAAI,CAACnE,KAAK,CAAC3B,IAAI;IAC1B,IAAI,IAAA+F,sBAAe,EAACD,EAAE,CAAC,KAAK,IAAI,CAAC1C,SAAS,CAACqC,KAAK,IAAI,CAAC,IAAI,CAAClE,KAAK,GAAO,CAAC,CAAC,EAAE;MACxE,IAAIyE,IAAI,GAAG,IAAAR,8BAAuB,EAACM,EAAE,CAAC;MACtC,IAAIE,IAAI,GAAGX,OAAO,EAAE;QAClB,IAAIS,EAAE,OAAgB,EAAE;UACtB,IAAI,CAACG,YAAY,CAAC,kBAAkB,CAAC;UACrC,IAAI,IAAI,CAACtE,KAAK,CAACuE,0BAA0B,EAAE;YACzC,OAAO5C,IAAI;UACb;UACA,IAAI,CAAC6C,4BAA4B,CAAC7C,IAAI,EAAE8B,YAAY,CAAC;QACvD;QACA,MAAM/C,IAAI,GAAG,IAAI,CAACC,WAAW,CAC3B8C,YACF,CAAC;QACD/C,IAAI,CAACiB,IAAI,GAAGA,IAAI;QAChBjB,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACnC,KAAK,CAACrB,KAAK;QAEhC,MAAM8F,OAAO,GAAGN,EAAE,OAAiB,IAAIA,EAAE,OAAkB;QAC3D,MAAMO,QAAQ,GAAGP,EAAE,OAAyB;QAE5C,IAAIO,QAAQ,EAAE;UAGZL,IAAI,GAAG,IAAAR,8BAAuB,IAAc,CAAC;QAC/C;QAEA,IAAI,CAACnB,IAAI,CAAC,CAAC;QAEX,IACEyB,EAAE,OAAgB,IAClB,IAAI,CAACQ,SAAS,CAAC,CAAC,kBAAkB,EAAE;UAAEC,QAAQ,EAAE;QAAU,CAAC,CAAC,CAAC,EAC7D;UACA,IAAI,IAAI,CAAC5E,KAAK,CAAC3B,IAAI,OAAc,IAAI,IAAI,CAACoD,SAAS,CAACoD,QAAQ,EAAE;YAC5D,MAAM,IAAI,CAACjG,KAAK,CAACC,kBAAM,CAACiG,gCAAgC,EAAE;cACxD/F,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;YACjB,CAAC,CAAC;UACJ;QACF;QAEAE,IAAI,CAACiC,KAAK,GAAG,IAAI,CAACoC,oBAAoB,CAACZ,EAAE,EAAEE,IAAI,CAAC;QAChD,MAAMW,YAAY,GAAG,IAAI,CAAChE,UAAU,CAClCN,IAAI,EACJ+D,OAAO,IAAIC,QAAQ,GAAG,mBAAmB,GAAG,kBAC9C,CAAC;QAMD,MAAMO,MAAM,GAAG,IAAI,CAACjF,KAAK,CAAC3B,IAAI;QAC9B,IACGqG,QAAQ,KAAKO,MAAM,OAAiB,IAAIA,MAAM,OAAkB,CAAC,IACjER,OAAO,IAAIQ,MAAM,OAA0B,EAC5C;UACA,MAAM,IAAI,CAACrG,KAAK,CAACC,kBAAM,CAACqG,yBAAyB,EAAE;YACjDnG,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;UACjB,CAAC,CAAC;QACJ;QAEA,OAAO,IAAI,CAACgD,WAAW,CAACwB,YAAY,EAAEvB,YAAY,EAAEC,OAAO,CAAC;MAC9D;IACF;IACA,OAAO/B,IAAI;EACb;EAKAoD,oBAAoBA,CAElBZ,EAAa,EACbE,IAAY,EACE;IACd,MAAM7D,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,QAAQ2D,EAAE;MACR;QACE,QAAQ,IAAI,CAACgB,eAAe,CAAC,kBAAkB,EAAE,UAAU,CAAC;UAC1D,KAAK,MAAM;YACT,OAAO,IAAI,CAACC,uBAAuB,CAAC,MAAM;cACxC,OAAO,IAAI,CAACC,iBAAiB,CAAC,CAAC;YACjC,CAAC,CAAC;UAEJ,KAAK,OAAO;YACV,OAAO,IAAI,CAACD,uBAAuB,CAAC,MAAM;cACxC,IAAI,IAAI,CAAC3D,SAAS,CAACC,QAAQ,IAAI,IAAI,CAACF,YAAY,IAAU,CAAC,EAAE;gBAC3D,MAAM,IAAI,CAAC5C,KAAK,CAACC,kBAAM,CAACyG,iBAAiB,EAAE;kBACzCvG,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;gBACjB,CAAC,CAAC;cACJ;cACA,OAAO,IAAI,CAAC+E,6BAA6B,CACvC,IAAI,CAACC,wBAAwB,CAACrB,EAAE,EAAEE,IAAI,CAAC,EACvC7D,QACF,CAAC;YACH,CAAC,CAAC;UAEJ,KAAK,QAAQ;YACX,OAAO,IAAI,CAACiF,8BAA8B,CAAC,MAAM;cAC/C,OAAO,IAAI,CAACC,uBAAuB,CAACrB,IAAI,CAAC;YAC3C,CAAC,CAAC;QACN;MAGF;QACE,OAAO,IAAI,CAACmB,wBAAwB,CAACrB,EAAE,EAAEE,IAAI,CAAC;IAClD;EACF;EAKAmB,wBAAwBA,CAEtBrB,EAAa,EACbE,IAAY,EACE;IACd,MAAM7D,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IAEpC,OAAO,IAAI,CAACgD,WAAW,CACrB,IAAI,CAACH,wBAAwB,CAAC,CAAC,EAC/B7C,QAAQ,EACR,IAAAmF,8BAAuB,EAACxB,EAAE,CAAC,GAAGE,IAAI,GAAG,CAAC,GAAGA,IAC3C,CAAC;EACH;EAEAgB,iBAAiBA,CAAA,EAA6B;IAAA,IAAAO,WAAA;IAC5C,MAAM;MAAEpF;IAAS,CAAC,GAAG,IAAI,CAACR,KAAK;IAC/B,MAAM6F,IAAI,GAAG,IAAI,CAACpF,gBAAgB,CAAC,CAAC;IACpC,MAAMqF,mBAAmB,GAAGC,2DAAmC,CAACC,GAAG,CAEjEH,IAAI,CAACxH,IACP,CAAC;IAGD,IAAIyH,mBAAmB,IAAI,GAAAF,WAAA,GAACC,IAAI,CAACI,KAAK,aAAVL,WAAA,CAAYM,aAAa,GAAE;MACrD,IAAI,CAACtH,KAAK,CAACC,kBAAM,CAACsH,uBAAuB,EAAE;QACzCpH,EAAE,EAAEyB,QAAQ;QAEZnC,IAAI,EAAEwH,IAAI,CAACxH;MACb,CAAC,CAAC;IACJ;IACA,IAAI,CAAC,IAAI,CAAC+H,qCAAqC,CAAC,CAAC,EAAE;MAEjD,IAAI,CAACxH,KAAK,CAACC,kBAAM,CAACwH,eAAe,EAAE;QAAEtH,EAAE,EAAEyB;MAAS,CAAC,CAAC;IACtD;IAEA,OAAOqF,IAAI;EACb;EAEAS,0BAA0BA,CACxB5F,IAAmD,EACnD;IACA,IAAI,IAAI,CAACd,KAAK,GAAY,CAAC,EAAE;MAC3B,IAAI,CAAChB,KAAK,CAACC,kBAAM,CAAC0H,kCAAkC,EAAE;QACpDxH,EAAE,EAAE2B,IAAI,CAAC8F;MACX,CAAC,CAAC;IACJ;EACF;EAIAjD,eAAeA,CAEbnF,mBAA6C,EAC7CqI,QAAkB,EACJ;IACd,MAAMjG,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMkG,OAAO,GAAG,IAAI,CAAClF,YAAY,GAAU,CAAC;IAE5C,IAAIkF,OAAO,IAAI,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACpC,IAAI,CAACjE,IAAI,CAAC,CAAC;MACX,MAAMpD,IAAI,GAAG,IAAI,CAACsH,UAAU,CAACpG,QAAQ,CAAC;MACtC,IAAI,CAACiG,QAAQ,EAAE,IAAI,CAACH,0BAA0B,CAAChH,IAAI,CAAC;MACpD,OAAOA,IAAI;IACb;IACA,MAAMuH,MAAM,GAAG,IAAI,CAACjH,KAAK,GAAU,CAAC;IACpC,MAAMc,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAyC,CAAC;IACrE,IAAI,IAAAC,oBAAa,EAAC,IAAI,CAAC/G,KAAK,CAAC3B,IAAI,CAAC,EAAE;MAClCqC,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACnC,KAAK,CAACrB,KAAK;MAChC+B,IAAI,CAACsG,MAAM,GAAG,IAAI;MAElB,IAAI,IAAI,CAACpH,KAAK,GAAU,CAAC,EAAE;QACzB,IAAI,CAAC0E,YAAY,CAAC,kBAAkB,CAAC;MACvC;MACA,MAAM2C,QAAQ,GAAG,IAAI,CAACrH,KAAK,GAAW,CAAC;MACvC,IAAI,CAAC8C,IAAI,CAAC,CAAC;MAEXhC,IAAI,CAAC8F,QAAQ,GAAG,IAAI,CAACjD,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;MAEhD,IAAI,CAACT,qBAAqB,CAAC1E,mBAAmB,EAAE,IAAI,CAAC;MAErD,IAAI,IAAI,CAAC4B,KAAK,CAACkH,MAAM,IAAID,QAAQ,EAAE;QACjC,MAAME,GAAG,GAAGzG,IAAI,CAAC8F,QAAQ;QAEzB,IAAIW,GAAG,CAAC9I,IAAI,KAAK,YAAY,EAAE;UAC7B,IAAI,CAACO,KAAK,CAACC,kBAAM,CAACuI,YAAY,EAAE;YAAErI,EAAE,EAAE2B;UAAK,CAAC,CAAC;QAC/C,CAAC,MAAM,IAAI,IAAI,CAAC2G,wBAAwB,CAACF,GAAG,CAAC,EAAE;UAC7C,IAAI,CAACvI,KAAK,CAACC,kBAAM,CAACyI,kBAAkB,EAAE;YAAEvI,EAAE,EAAE2B;UAAK,CAAC,CAAC;QACrD;MACF;MAEA,IAAI,CAACmG,MAAM,EAAE;QACX,IAAI,CAACJ,QAAQ,EAAE;UACb,IAAI,CAACH,0BAA0B,CAAC5F,IAAiC,CAAC;QACpE;QACA,OAAO,IAAI,CAACM,UAAU,CAACN,IAAI,EAAE,iBAAiB,CAAC;MACjD;IACF;IAEA,MAAMpB,IAAI,GAAG,IAAI,CAACiI,WAAW,CAE3B7G,IAAI,EACJmG,MAAM,EACNzI,mBACF,CAAC;IAED,IAAIsI,OAAO,EAAE;MACX,MAAM;QAAErI;MAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;MAC3B,MAAMwH,UAAU,GAAG,IAAI,CAAC7C,SAAS,CAAC,aAAa,CAAC,GAC5C,IAAA8C,8BAAuB,EAACpJ,IAAI,CAAC,GAC7B,IAAAoJ,8BAAuB,EAACpJ,IAAI,CAAC,IAAI,CAAC,IAAI,CAACuB,KAAK,GAAU,CAAC;MAC3D,IAAI4H,UAAU,IAAI,CAAC,IAAI,CAACE,gBAAgB,CAAC,CAAC,EAAE;QAC1C,IAAI,CAACC,cAAc,CAAC9I,kBAAM,CAAC+I,sBAAsB,EAAE;UAAE7I,EAAE,EAAEyB;QAAS,CAAC,CAAC;QACpE,OAAO,IAAI,CAACoG,UAAU,CAACpG,QAAQ,CAAC;MAClC;IACF;IAEA,OAAOlB,IAAI;EACb;EAGAiI,WAAWA,CAET7G,IAAkB,EAClBmG,MAAe,EACfzI,mBAA6C,EAC/B;IACd,IAAIyI,MAAM,EAAE;MAEV,MAAMgB,oBAAoB,GAAGnH,IAAkC;MAC/D,IAAI,CAACkC,SAAS,CAACiF,oBAAoB,CAACrB,QAAQ,EAAE;QAC5C3D,EAAE,EAAE,IAAI,CAAC7B,UAAU,CAAC6G,oBAAoB,EAAE,kBAAkB;MAC9D,CAAC,CAAC;MACF,OAAOnH,IAAI;IACb;IAEA,MAAMF,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,IAAIlB,IAAI,GAAG,IAAI,CAACwI,mBAAmB,CAAC1J,mBAAmB,CAAC;IACxD,IAAI,IAAI,CAAC0E,qBAAqB,CAAC1E,mBAAmB,EAAE,KAAK,CAAC,EAAE,OAAOkB,IAAI;IACvE,OAAO,IAAAyI,qBAAc,EAAC,IAAI,CAAC/H,KAAK,CAAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC2J,kBAAkB,CAAC,CAAC,EAAE;MACpE,MAAMtH,IAAI,GAAG,IAAI,CAACC,WAAW,CAAqBH,QAAQ,CAAC;MAC3DE,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACnC,KAAK,CAACrB,KAAK;MAChC+B,IAAI,CAACsG,MAAM,GAAG,KAAK;MACnBtG,IAAI,CAAC8F,QAAQ,GAAGlH,IAAI;MACpB,IAAI,CAACoD,IAAI,CAAC,CAAC;MACX,IAAI,CAACE,SAAS,CAACtD,IAAI,EAAE;QACnBuD,EAAE,EAAGvD,IAAI,GAAG,IAAI,CAAC0B,UAAU,CAACN,IAAI,EAAE,kBAAkB;MACtD,CAAC,CAAC;IACJ;IACA,OAAOpB,IAAI;EACb;EAIAwI,mBAAmBA,CAEjB1J,mBAA6C,EAC/B;IACd,MAAMoC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMjB,gBAAgB,GAAG,IAAI,CAACS,KAAK,CAACT,gBAAgB;IACpD,MAAMD,IAAI,GAAG,IAAI,CAAC2I,aAAa,CAAC7J,mBAAmB,CAAC;IAEpD,IAAI,IAAI,CAACiB,oBAAoB,CAACC,IAAI,EAAEC,gBAAgB,CAAC,EAAE;MACrD,OAAOD,IAAI;IACb;IAEA,OAAO,IAAI,CAAC4I,eAAe,CAAC5I,IAAI,EAAEkB,QAAQ,CAAC;EAC7C;EAEA0H,eAAeA,CAEbC,IAAkB,EAClB3H,QAAkB,EAClB4H,OAAwB,EACV;IACd,MAAMpI,KAAK,GAAG;MACZqI,mBAAmB,EAAE,KAAK;MAC1BC,eAAe,EAAE,IAAI,CAACC,oBAAoB,CAACJ,IAAI,CAAC;MAChDK,IAAI,EAAE;IACR,CAAC;IACD,GAAG;MACDL,IAAI,GAAG,IAAI,CAACM,cAAc,CAACN,IAAI,EAAE3H,QAAQ,EAAE4H,OAAO,EAAEpI,KAAK,CAAC;MAG1DA,KAAK,CAACsI,eAAe,GAAG,KAAK;IAC/B,CAAC,QAAQ,CAACtI,KAAK,CAACwI,IAAI;IACpB,OAAOL,IAAI;EACb;EAMAM,cAAcA,CAEZN,IAAkB,EAClB3H,QAAkB,EAClB4H,OAAmC,EACnCpI,KAA4B,EACd;IACd,MAAM;MAAE3B;IAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAC3B,IAAI,CAACoI,OAAO,IAAI/J,IAAI,OAAmB,EAAE;MACvC,OAAO,IAAI,CAACqK,SAAS,CAACP,IAAI,EAAE3H,QAAQ,EAAE4H,OAAO,EAAEpI,KAAK,CAAC;IACvD,CAAC,MAAM,IAAI,IAAA2I,sBAAe,EAACtK,IAAI,CAAC,EAAE;MAChC,OAAO,IAAI,CAACuK,6BAA6B,CAACT,IAAI,EAAE3H,QAAQ,EAAER,KAAK,CAAC;IAClE;IAEA,IAAI6I,QAAQ,GAAG,KAAK;IAEpB,IAAIxK,IAAI,OAAmB,EAAE;MAC3B,IAAI+J,OAAO,EAAE;QACX,IAAI,CAACxJ,KAAK,CAACC,kBAAM,CAACiK,qBAAqB,EAAE;UACvC/J,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;QACjB,CAAC,CAAC;QACF,IAAI,IAAI,CAACuI,iBAAiB,CAAC,CAAC,OAA8B,EAAE;UAE1D/I,KAAK,CAACwI,IAAI,GAAG,IAAI;UACjB,OAAOL,IAAI;QACb;MACF;MACAnI,KAAK,CAACqI,mBAAmB,GAAGQ,QAAQ,GAAG,IAAI;MAC3C,IAAI,CAACnG,IAAI,CAAC,CAAC;IACb;IAEA,IAAI,CAAC0F,OAAO,IAAI,IAAI,CAACxI,KAAK,GAAU,CAAC,EAAE;MACrC,OAAO,IAAI,CAACoJ,+BAA+B,CACzCb,IAAI,EACJ3H,QAAQ,EACRR,KAAK,EACL6I,QACF,CAAC;IACH,CAAC,MAAM;MACL,MAAMtK,QAAQ,GAAG,IAAI,CAACsC,GAAG,EAAY,CAAC;MACtC,IAAItC,QAAQ,IAAIsK,QAAQ,IAAI,IAAI,CAAChI,GAAG,GAAO,CAAC,EAAE;QAC5C,OAAO,IAAI,CAACoI,WAAW,CAACd,IAAI,EAAE3H,QAAQ,EAAER,KAAK,EAAEzB,QAAQ,EAAEsK,QAAQ,CAAC;MACpE,CAAC,MAAM;QACL7I,KAAK,CAACwI,IAAI,GAAG,IAAI;QACjB,OAAOL,IAAI;MACb;IACF;EACF;EAMAc,WAAWA,CAETd,IAAkB,EAClB3H,QAAkB,EAClBR,KAA4B,EAC5BzB,QAAiB,EACjBsK,QAAiB,EACgC;IACjD,MAAMnI,IAAI,GAAG,IAAI,CAACC,WAAW,CAE3BH,QAAQ,CAAC;IACXE,IAAI,CAACwI,MAAM,GAAGf,IAAI;IAClBzH,IAAI,CAACnC,QAAQ,GAAGA,QAAQ;IACxB,IAAIA,QAAQ,EAAE;MACZmC,IAAI,CAACyI,QAAQ,GAAG,IAAI,CAACxJ,eAAe,CAAC,CAAC;MACtC,IAAI,CAACwD,MAAM,EAAY,CAAC;IAC1B,CAAC,MAAM,IAAI,IAAI,CAACvD,KAAK,IAAe,CAAC,EAAE;MACrC,IAAIuI,IAAI,CAAC9J,IAAI,KAAK,OAAO,EAAE;QACzB,IAAI,CAACO,KAAK,CAACC,kBAAM,CAACuK,iBAAiB,EAAE;UAAErK,EAAE,EAAEyB;QAAS,CAAC,CAAC;MACxD;MACA,IAAI,CAACyD,UAAU,CAACC,cAAc,CAAC,IAAI,CAAClE,KAAK,CAACrB,KAAK,EAAE,IAAI,CAACqB,KAAK,CAACQ,QAAQ,CAAC;MACrEE,IAAI,CAACyI,QAAQ,GAAG,IAAI,CAAC7F,gBAAgB,CAAC,CAAC;IACzC,CAAC,MAAM;MACL5C,IAAI,CAACyI,QAAQ,GAAG,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC;IAC5C;IAEA,IAAIrJ,KAAK,CAACqI,mBAAmB,EAAE;MAC5B3H,IAAI,CAAgCmI,QAAQ,GAAGA,QAAQ;MACxD,OAAO,IAAI,CAAC7H,UAAU,CAACN,IAAI,EAAE,0BAA0B,CAAC;IAC1D,CAAC,MAAM;MACL,OAAO,IAAI,CAACM,UAAU,CAACN,IAAI,EAAE,kBAAkB,CAAC;IAClD;EACF;EAGAgI,SAASA,CAEPP,IAAkB,EAClB3H,QAAkB,EAClB4H,OAAmC,EACnCpI,KAA4B,EACd;IACd,MAAMU,IAAI,GAAG,IAAI,CAACC,WAAW,CAACH,QAAQ,CAAC;IACvCE,IAAI,CAACwI,MAAM,GAAGf,IAAI;IAClB,IAAI,CAACzF,IAAI,CAAC,CAAC;IACXhC,IAAI,CAAC4I,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACpCvJ,KAAK,CAACwI,IAAI,GAAG,IAAI;IACjB,OAAO,IAAI,CAACN,eAAe,CACzB,IAAI,CAAClH,UAAU,CAACN,IAAI,EAAE,gBAAgB,CAAC,EACvCF,QAAQ,EACR4H,OACF,CAAC;EACH;EAMAY,+BAA+BA,CAE7Bb,IAAkB,EAClB3H,QAAkB,EAClBR,KAA4B,EAC5B6I,QAAiB,EACH;IACd,MAAMW,yBAAyB,GAAG,IAAI,CAACxJ,KAAK,CAACyJ,sBAAsB;IACnE,IAAIrL,mBAA4C,GAAG,IAAI;IAEvD,IAAI,CAAC4B,KAAK,CAACyJ,sBAAsB,GAAG,IAAI;IACxC,IAAI,CAAC/G,IAAI,CAAC,CAAC;IAEX,MAAMhC,IAAI,GAAG,IAAI,CAACC,WAAW,CAC3BH,QACF,CAAC;IACDE,IAAI,CAAC4I,MAAM,GAAGnB,IAAI;IAClB,MAAM;MAAEG,eAAe;MAAED;IAAoB,CAAC,GAAGrI,KAAK;IAEtD,IAAIsI,eAAe,EAAE;MACnB,IAAI,CAACoB,eAAe,CAACC,KAAK,CAAC,IAAAC,mCAAkB,EAAC,CAAC,CAAC;MAChDxL,mBAAmB,GAAG,IAAI2D,sBAAgB,CAAC,CAAC;IAC9C;IAEA,IAAIsG,mBAAmB,EAAE;MAEvB3H,IAAI,CAACmI,QAAQ,GAAGA,QAAQ;IAC1B;IAEA,IAAIA,QAAQ,EAAE;MACZnI,IAAI,CAACmJ,SAAS,GAAG,IAAI,CAACC,4BAA4B,GAAU,CAAC;IAC/D,CAAC,MAAM;MACLpJ,IAAI,CAACmJ,SAAS,GAAG,IAAI,CAACC,4BAA4B,KAEhD3B,IAAI,CAAC9J,IAAI,KAAK,QAAQ,EACtB8J,IAAI,CAAC9J,IAAI,KAAK,OAAO,EAErBqC,IAAI,EACJtC,mBACF,CAAC;IACH;IACA,IAAI4G,YAGyB,GAAG,IAAI,CAAC+E,oBAAoB,CACvDrJ,IAAI,EACJ2H,mBACF,CAAC;IAED,IAAIC,eAAe,IAAI,IAAI,CAAC0B,qBAAqB,CAAC,CAAC,IAAI,CAACnB,QAAQ,EAAE;MAEhE7I,KAAK,CAACwI,IAAI,GAAG,IAAI;MACjB,IAAI,CAAC/F,yBAAyB,CAACrE,mBAAmB,CAAC;MACnD,IAAI,CAACsL,eAAe,CAACO,iBAAiB,CAAC,CAAC;MACxC,IAAI,CAACP,eAAe,CAACQ,IAAI,CAAC,CAAC;MAC3BlF,YAAY,GAAG,IAAI,CAACmF,iCAAiC,CACnD,IAAI,CAACxJ,WAAW,CAA4BH,QAAQ,CAAC,EACrDwE,YACF,CAAC;IACH,CAAC,MAAM;MACL,IAAIsD,eAAe,EAAE;QACnB,IAAI,CAACxF,qBAAqB,CAAC1E,mBAAmB,EAAE,IAAI,CAAC;QACrD,IAAI,CAACsL,eAAe,CAACQ,IAAI,CAAC,CAAC;MAC7B;MACA,IAAI,CAACE,qBAAqB,CAACpF,YAAY,CAAC;IAC1C;IAEA,IAAI,CAAChF,KAAK,CAACyJ,sBAAsB,GAAGD,yBAAyB;IAE7D,OAAOxE,YAAY;EACrB;EAEAoF,qBAAqBA,CACnB1J,IAAiD,EACjD2J,mBAA6B,EAC7B;IACA,IAAI,CAACC,oBAAoB,CAAC5J,IAAI,CAACmJ,SAAS,EAAEQ,mBAAmB,CAAC;EAChE;EAIAzB,6BAA6BA,CAE3BT,IAAkB,EAClB3H,QAAkB,EAClBR,KAA4B,EACA;IAC5B,MAAMU,IAAI,GAAG,IAAI,CAACC,WAAW,CAA6BH,QAAQ,CAAC;IACnEE,IAAI,CAAC6J,GAAG,GAAGpC,IAAI;IACfzH,IAAI,CAAC8J,KAAK,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAAC;IACrC,IAAIzK,KAAK,CAACqI,mBAAmB,EAAE;MAC7B,IAAI,CAACzJ,KAAK,CAACC,kBAAM,CAAC6L,0BAA0B,EAAE;QAAE3L,EAAE,EAAEyB;MAAS,CAAC,CAAC;IACjE;IACA,OAAO,IAAI,CAACQ,UAAU,CAACN,IAAI,EAAE,0BAA0B,CAAC;EAC1D;EAEA6H,oBAAoBA,CAACJ,IAAkB,EAAW;IAChD,OACEA,IAAI,CAAC9J,IAAI,KAAK,YAAY,IAC1B8J,IAAI,CAACzJ,IAAI,KAAK,OAAO,IACrB,IAAI,CAACsB,KAAK,CAAC2K,aAAa,CAACrI,KAAK,KAAK6F,IAAI,CAACyC,GAAG,IAC3C,CAAC,IAAI,CAAC5C,kBAAkB,CAAC,CAAC,IAE1BG,IAAI,CAACyC,GAAG,GAAGzC,IAAI,CAAChJ,KAAK,KAAK,CAAC,IAC3BgJ,IAAI,CAAChJ,KAAK,KAAK,IAAI,CAACa,KAAK,CAACT,gBAAgB;EAE9C;EAEAsL,4BAA4BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAAClG,SAAS,CAAC,kBAAkB,CAAC,EAAE;MACvC,IAAI,CAACL,YAAY,CAAC,kBAAkB,CAAC;IACvC;EACF;EAEAyF,oBAAoBA,CAClBrJ,IAAe,EACfmI,QAAiB,EACd;IACH,IAAInI,IAAI,CAAC4I,MAAM,CAACjL,IAAI,KAAK,QAAQ,EAAE;MACjC,IAAIqC,IAAI,CAACmJ,SAAS,CAACiB,MAAM,KAAK,CAAC,EAAE;QAGxB;UACL,IAAI,CAAC,IAAI,CAACnG,SAAS,CAAC,kBAAkB,CAAC,EAAE;YACvC,IAAI,CAACkG,4BAA4B,CAAC,CAAC;UACrC;QACF;MACF;MACA,IAAInK,IAAI,CAACmJ,SAAS,CAACiB,MAAM,KAAK,CAAC,IAAIpK,IAAI,CAACmJ,SAAS,CAACiB,MAAM,GAAG,CAAC,EAAE;QAC5D,IAAI,CAAClM,KAAK,CAACC,kBAAM,CAACkM,eAAe,EAAE;UACjChM,EAAE,EAAE2B,IAAI;UACRsK,gBAAgB,EACd,IAAI,CAACrG,SAAS,CAAC,kBAAkB,CAAC,IAClC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,IAClC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,GAC9B,CAAC,GACD;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,KAAK,MAAMwC,GAAG,IAAIzG,IAAI,CAACmJ,SAAS,EAAE;UAChC,IAAI1C,GAAG,CAAC9I,IAAI,KAAK,eAAe,EAAE;YAChC,IAAI,CAACO,KAAK,CAACC,kBAAM,CAACoM,wBAAwB,EAAE;cAAElM,EAAE,EAAEoI;YAAI,CAAC,CAAC;UAC1D;QACF;MACF;IACF;IACA,OAAO,IAAI,CAACnG,UAAU,CACpBN,IAAI,EACJmI,QAAQ,GAAG,wBAAwB,GAAG,gBACxC,CAAC;EACH;EAEAiB,4BAA4BA,CAE1BoB,KAAgB,EAChBC,aAAuB,EACvBC,gBAA0B,EAC1BC,YAA4B,EAC5BjN,mBAA6C,EACL;IACxC,MAAMkN,IAAoB,GAAG,EAAE;IAC/B,IAAIC,KAAK,GAAG,IAAI;IAChB,MAAMC,6BAA6B,GAAG,IAAI,CAACxL,KAAK,CAACuE,0BAA0B;IAC3E,IAAI,CAACvE,KAAK,CAACuE,0BAA0B,GAAG,KAAK;IAE7C,OAAO,CAAC,IAAI,CAAC1D,GAAG,CAACqK,KAAK,CAAC,EAAE;MACvB,IAAIK,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACpI,MAAM,GAAS,CAAC;QACrB,IAAI,IAAI,CAACvD,KAAK,CAACsL,KAAK,CAAC,EAAE;UACrB,IACEC,aAAa,IACb,CAAC,IAAI,CAACxG,SAAS,CAAC,kBAAkB,CAAC,IACnC,CAAC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,IACnC,CAAC,IAAI,CAACA,SAAS,CAAC,kBAAkB,CAAC,EACnC;YACA,IAAI,CAAC/F,KAAK,CAACC,kBAAM,CAAC4M,+BAA+B,EAAE;cACjD1M,EAAE,EAAE,IAAI,CAACiB,KAAK,CAAC0L;YACjB,CAAC,CAAC;UACJ;UACA,IAAIL,YAAY,EAAE;YAChB,IAAI,CAACM,2BAA2B,CAACN,YAAY,CAAC;UAChD;UACA,IAAI,CAAC3I,IAAI,CAAC,CAAC;UACX;QACF;MACF;MAEA4I,IAAI,CAACxK,IAAI,CACP,IAAI,CAAC8K,iBAAiB,CAAC,KAAK,EAAExN,mBAAmB,EAAEgN,gBAAgB,CACrE,CAAC;IACH;IAEA,IAAI,CAACpL,KAAK,CAACuE,0BAA0B,GAAGiH,6BAA6B;IAErE,OAAOF,IAAI;EACb;EAEAtB,qBAAqBA,CAAA,EAAY;IAC/B,OAAO,IAAI,CAACpK,KAAK,GAAS,CAAC,IAAI,CAAC,IAAI,CAACoI,kBAAkB,CAAC,CAAC;EAC3D;EAEAmC,iCAAiCA,CAE/BzJ,IAAuC,EACvCmB,IAAsB,EACK;IAAA,IAAAgK,WAAA;IAC3B,IAAI,CAACC,iCAAiC,CAACjK,IAAI,CAAC;IAC5C,IAAI,CAACsB,MAAM,GAAS,CAAC;IACrB,IAAI,CAAC4I,oBAAoB,CACvBrL,IAAI,EACJmB,IAAI,CAACgI,SAAS,EACd,IAAI,GAAAgC,WAAA,GACJhK,IAAI,CAACoE,KAAK,qBAAV4F,WAAA,CAAYG,gBACd,CAAC;IAED,IAAInK,IAAI,CAACoK,aAAa,EAAE;MACtB,IAAAC,0BAAgB,EAACxL,IAAI,EAAEmB,IAAI,CAACoK,aAAa,CAAC;IAC5C;IAEA,IAAIpK,IAAI,CAACyH,MAAM,CAAC6C,gBAAgB,EAAE;MAChC,IAAAD,0BAAgB,EAACxL,IAAI,EAAEmB,IAAI,CAACyH,MAAM,CAAC6C,gBAAgB,CAAC;IACtD;IACA,OAAOzL,IAAI;EACb;EAIA6I,eAAeA,CAAA,EAA6B;IAC1C,MAAM/I,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,OAAO,IAAI,CAAC0H,eAAe,CAAC,IAAI,CAACD,aAAa,CAAC,CAAC,EAAEzH,QAAQ,EAAE,IAAI,CAAC;EACnE;EAcAyH,aAAaA,CAEX7J,mBAA6C,EAC/B;IACd,IAAIsC,IAAI;IACR,IAAI0L,UAAgC,GAAG,IAAI;IAE3C,MAAM;MAAE/N;IAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAC3B,QAAQ3B,IAAI;MACV;QACE,OAAO,IAAI,CAACgO,UAAU,CAAC,CAAC;MAE1B;QACE3L,IAAI,GAAG,IAAI,CAACoG,SAAS,CAA4B,CAAC;QAClD,IAAI,CAACpE,IAAI,CAAC,CAAC;QAEX,IAAI,IAAI,CAAC9C,KAAK,GAAO,CAAC,EAAE;UACtB,OAAO,IAAI,CAAC0M,uBAAuB,CAAC5L,IAA8B,CAAC;QACrE;QAEA,IAAI,CAAC,IAAI,CAACd,KAAK,GAAU,CAAC,EAAE;UAC1B,IAAI,CAAChB,KAAK,CAACC,kBAAM,CAAC0N,iBAAiB,EAAE;YACnCxN,EAAE,EAAE,IAAI,CAACiB,KAAK,CAAC0L;UACjB,CAAC,CAAC;QACJ;QACA,OAAO,IAAI,CAAC1K,UAAU,CAACN,IAAI,EAAE,QAAQ,CAAC;MACxC;QACEA,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAC,CAAC;QACvB,IAAI,CAACpE,IAAI,CAAC,CAAC;QACX,OAAO,IAAI,CAAC1B,UAAU,CAACN,IAAI,EAAE,gBAAgB,CAAC;MAEhD;QAAa;UACX,OAAO,IAAI,CAAC8L,OAAO,CAAC,IAAI,CAAC1F,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;QAC9C;MAEA;MACA;QAAqB;UACnB,IAAI,CAAC2F,UAAU,CAAC,CAAC;UACjB,OAAO,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC1M,KAAK,CAACrB,KAAK,CAAC;QAClD;MAEA;QACE,OAAO,IAAI,CAACgO,mBAAmB,CAAC,IAAI,CAAC3M,KAAK,CAACrB,KAAK,CAAC;MAEnD;QACE,OAAO,IAAI,CAACiO,kBAAkB,CAAC,IAAI,CAAC5M,KAAK,CAACrB,KAAK,CAAC;MAElD;QACE,OAAO,IAAI,CAACkO,mBAAmB,CAAC,IAAI,CAAC7M,KAAK,CAACrB,KAAK,CAAC;MAEnD;QACE,OAAO,IAAI,CAACmO,kBAAkB,CAAC,IAAI,CAAC9M,KAAK,CAACrB,KAAK,CAAC;MAElD;QACE,OAAO,IAAI,CAACoO,gBAAgB,CAAC,CAAC;MAEhC;QACE,OAAO,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC;MACvC;QACE,OAAO,IAAI,CAACA,mBAAmB,CAAC,KAAK,CAAC;MAExC;QAAgB;UACd,MAAMC,UAAU,GAAG,IAAI,CAACjN,KAAK,CAACT,gBAAgB,KAAK,IAAI,CAACS,KAAK,CAACb,KAAK;UACnE,OAAO,IAAI,CAAC+N,kCAAkC,CAACD,UAAU,CAAC;QAC5D;MAEA;MACA;QAAsB;UACpB,OAAO,IAAI,CAACE,cAAc,CACxB,IAAI,CAACnN,KAAK,CAAC3B,IAAI,MAAmB,QAA+B,EAC9C,KAAK,EACV,IAChB,CAAC;QACH;MACA;QAAkB;UAChB,OAAO,IAAI,CAAC8O,cAAc,IAEL,IAAI,EACT,KAAK,EACnB/O,mBACF,CAAC;QACH;MACA;MACA;QAAoB;UAClB,OAAO,IAAI,CAACgP,eAAe,CACzB,IAAI,CAACpN,KAAK,CAAC3B,IAAI,MAAiB,QAA2B,EAC3C,KAAK,EACN,IACjB,CAAC;QACH;MACA;QAAgB;UACd,OAAO,IAAI,CAAC+O,eAAe,IAET,KAAK,EACN,KAAK,EACpBhP,mBACF,CAAC;QACH;MACA;QACE,OAAO,IAAI,CAACiP,2BAA2B,CAAC,CAAC;MAE3C;QACEjB,UAAU,GAAG,IAAI,CAACkB,eAAe,CAAC,CAAC;MAErC;QACE,OAAO,IAAI,CAACC,UAAU,CACpB,IAAI,CAACC,mBAAmB,CAACpB,UAAU,EAAE,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,EACtD,KACF,CAAC;MAEH;QACE,OAAO,IAAI,CAAC2G,mBAAmB,CAAC,CAAC;MAEnC;MACA;QACE,OAAO,IAAI,CAAChD,aAAa,CAAC,KAAK,CAAC;MAIlC;QAAqB;UACnB/J,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAC,CAAC;UACvB,IAAI,CAACpE,IAAI,CAAC,CAAC;UACXhC,IAAI,CAACwI,MAAM,GAAG,IAAI;UAClB,MAAMI,MAAM,GAAI5I,IAAI,CAAC4I,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC,CAAE;UACrD,IAAID,MAAM,CAACjL,IAAI,KAAK,kBAAkB,EAAE;YACtC,OAAO,IAAI,CAAC2C,UAAU,CAACN,IAAI,EAAE,gBAAgB,CAAC;UAChD,CAAC,MAAM;YACL,MAAM,IAAI,CAAC9B,KAAK,CAACC,kBAAM,CAAC6O,eAAe,EAAE;cAAE3O,EAAE,EAAEuK;YAAO,CAAC,CAAC;UAC1D;QACF;MAEA;QAAqB;UAOnB,IAAI,CAAC1K,KAAK,CAACC,kBAAM,CAACkF,mBAAmB,EAAE;YACrChF,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ,QAAQ;YACvBwD,cAAc,EAAE,IAAI,CAAChE,KAAK,CAACrB;UAC7B,CAAC,CAAC;UACF,OAAO,IAAI,CAAC2E,gBAAgB,CAAC,CAAC;QAChC;MAEA;QAAsB;UACpB,OAAO,IAAI,CAACqK,iCAAiC,KAAY,GAAG,CAAC;QAC/D;MAEA;QAAmB;UACjB,OAAO,IAAI,CAACA,iCAAiC,KAAgB,GAAG,CAAC;QACnE;MAEA;MACA;QAAkB;UAChB,OAAO,IAAI,CAACC,mBAAmB,CAAC,MAAM,CAAC;QACzC;MAEA;MACA;MACA;QAAc;UACZ,MAAMC,YAAY,GAAG,IAAI,CAAC1I,eAAe,CACvC,kBAAkB,EAClB,UACF,CAAC;UAED,IAAI0I,YAAY,EAAE;YAChB,OAAO,IAAI,CAACD,mBAAmB,CAACC,YAAY,CAAC;UAC/C;UACA,IAAI,CAAChO,UAAU,CAAC,CAAC;UACjB;QACF;MAEA;QAAY;UACV,MAAMiO,WAAW,GAAG,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;UACjE,IACE,IAAAC,6BAAiB,EAACJ,WAAW,CAAC,IAC9BA,WAAW,OAA0B,EACrC;YACA,IAAI,CAACK,eAAe,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;UACrD,CAAC,MAAM;YACL,IAAI,CAACtO,UAAU,CAAC,CAAC;UACnB;UACA;QACF;MAEA;QACE,IAAI,IAAAmC,wBAAiB,EAAC3D,IAAI,CAAC,EAAE;UAC3B,IACE,IAAI,CAACmD,YAAY,IAAW,CAAC,IAC7B,IAAI,CAAC4M,uBAAuB,CAAC,CAAC,QAA6B,EAC3D;YACA,OAAO,IAAI,CAACC,qBAAqB,CAAC,CAAC;UACrC;UACA,MAAMpB,UAAU,GAAG,IAAI,CAACjN,KAAK,CAACT,gBAAgB,KAAK,IAAI,CAACS,KAAK,CAACb,KAAK;UACnE,MAAMmP,WAAW,GAAG,IAAI,CAACtO,KAAK,CAACsO,WAAW;UAC1C,MAAMC,EAAE,GAAG,IAAI,CAAClF,eAAe,CAAC,CAAC;UAEjC,IACE,CAACiF,WAAW,IACZC,EAAE,CAAC7P,IAAI,KAAK,OAAO,IACnB,CAAC,IAAI,CAACsJ,kBAAkB,CAAC,CAAC,EAC1B;YACA,MAAM;cAAE3J;YAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;YAC3B,IAAI3B,IAAI,OAAiB,EAAE;cACzB,IAAI,CAACyN,iCAAiC,CAACyC,EAAE,CAAC;cAC1C,IAAI,CAAC7L,IAAI,CAAC,CAAC;cACX,OAAO,IAAI,CAAC8L,4BAA4B,CACtC,IAAI,CAACC,eAAe,CAACF,EAAE,CACzB,CAAC;YACH,CAAC,MAAM,IAAI,IAAAvM,wBAAiB,EAAC3D,IAAI,CAAC,EAAE;cAIlC,IAAI,IAAI,CAAC0K,iBAAiB,CAAC,CAAC,OAAuB,EAAE;gBAInD,OAAO,IAAI,CAAC2F,4BAA4B,CACtC,IAAI,CAACD,eAAe,CAACF,EAAE,CACzB,CAAC;cACH,CAAC,MAAM;gBAGL,OAAOA,EAAE;cACX;YACF,CAAC,MAAM,IAAIlQ,IAAI,OAAW,EAAE;cAC1B,IAAI,CAACyN,iCAAiC,CAACyC,EAAE,CAAC;cAC1C,OAAO,IAAI,CAAC/B,OAAO,CAAC,IAAI,CAACiC,eAAe,CAACF,EAAE,CAAC,EAAE,IAAI,CAAC;YACrD;UACF;UAEA,IACEtB,UAAU,IACV,IAAI,CAACrN,KAAK,GAAS,CAAC,IACpB,CAAC,IAAI,CAACoI,kBAAkB,CAAC,CAAC,EAC1B;YACA,IAAI,CAACtF,IAAI,CAAC,CAAC;YACX,OAAO,IAAI,CAACqJ,oBAAoB,CAC9B,IAAI,CAAC0C,eAAe,CAACF,EAAE,CAAC,EACxB,CAACA,EAAE,CAAC,EACJ,KACF,CAAC;UACH;UAEA,OAAOA,EAAE;QACX,CAAC,MAAM;UACL,IAAI,CAAC1O,UAAU,CAAC,CAAC;QACnB;IACJ;EACF;EAYA8N,iCAAiCA,CAC/BgB,cAAyB,EACzBC,eAAuB,EACT;IACd,MAAMf,YAAY,GAAG,IAAI,CAAC1I,eAAe,CAAC,kBAAkB,EAAE,UAAU,CAAC;IAEzE,IAAI0I,YAAY,EAAE;MAKhB,IAAI,CAAC7N,KAAK,CAAC3B,IAAI,GAAGsQ,cAAc;MAChC,IAAI,CAAC3O,KAAK,CAACrB,KAAK,GAAGiQ,eAAe;MAGlC,IAAI,CAAC5O,KAAK,CAAC6O,GAAG,EAAE;MAChB,IAAI,CAAC7O,KAAK,CAAC4K,GAAG,EAAE;MAGhB,IAAI,CAAC5K,KAAK,CAAC8O,MAAM,GAAG,IAAAC,wCAA8B,EAAC,IAAI,CAAC/O,KAAK,CAAC8O,MAAM,EAAE,CAAC,CAAC,CAAC;MAEzE,OAAO,IAAI,CAAClB,mBAAmB,CAACC,YAAY,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAAChO,UAAU,CAAC,CAAC;IACnB;EACF;EAQA+N,mBAAmBA,CAACC,YAAoB,EAAgB;IACtD,MAAMnN,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAmB,CAAC;IAC/C,MAAMtG,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IACpC,MAAMwO,SAAS,GAAG,IAAI,CAAChP,KAAK,CAAC3B,IAAI;IAGjC,IAAI,CAACqE,IAAI,CAAC,CAAC;IAKX,OAAO,IAAI,CAACuM,oBAAoB,CAACvO,IAAI,EAAEF,QAAQ,EAAEqN,YAAY,EAAEmB,SAAS,CAAC;EAC3E;EAeAC,oBAAoBA,CAClBvO,IAAoB,EACpBF,QAAkB,EAClBqN,YAAoB,EACpBmB,SAAoB,EACN;IACd,IACE,IAAI,CAACE,+BAA+B,CAACrB,YAAY,EAAErN,QAAQ,EAAEwO,SAAS,CAAC,EACvE;MAMA,MAAMG,QAAQ,GACZtB,YAAY,KAAK,OAAO,GACpB,+BAA+B,GAG/B,gBAAgB;MAEtB,IAAI,CAAC,IAAI,CAACuB,uCAAuC,CAAC,CAAC,EAAE;QACnD,IAAI,CAACxQ,KAAK,CAIRiP,YAAY,KAAK,OAAO,GACpBhP,kBAAM,CAACwQ,sBAAsB,GAE7BxQ,kBAAM,CAACyQ,gBAAgB,EAC3B;UAAEvQ,EAAE,EAAEyB;QAAS,CACjB,CAAC;MACH;MAIA,IAAI,CAAC+O,sBAAsB,CAAC,CAAC;MAE7B,OAAO,IAAI,CAACvO,UAAU,CAACN,IAAI,EAAEyO,QAAQ,CAAC;IACxC,CAAC,MAAM;MAEL,MAAM,IAAI,CAACvQ,KAAK,CAACC,kBAAM,CAAC2Q,0BAA0B,EAAE;QAClDzQ,EAAE,EAAEyB,QAAQ;QACZiP,KAAK,EAAE,IAAAC,qBAAc,EAACV,SAAS;MACjC,CAAC,CAAC;IACJ;EACF;EAWAE,+BAA+BA,CAC7BrB,YAAoB,EACpBrN,QAAkB,EAClBwO,SAAoB,EACX;IACT,QAAQnB,YAAY;MAClB,KAAK,MAAM;QAAE;UACX,OAAO,IAAI,CAAClJ,SAAS,CAAC,CACpB,kBAAkB,EAClB;YAEEgL,UAAU,EAAE,IAAAD,qBAAc,EAACV,SAAS;UACtC,CAAC,CACF,CAAC;QACJ;MACA,KAAK,OAAO;QACV,OAAOA,SAAS,OAAY;MAC9B;QACE,MAAM,IAAI,CAACpQ,KAAK,CAACC,kBAAM,CAAC+Q,0BAA0B,EAAE;UAAE7Q,EAAE,EAAEyB;QAAS,CAAC,CAAC;IACzE;EACF;EAGAkO,4BAA4BA,CAE1BhO,IAAuC,EACZ;IAG3B,IAAI,CAACe,SAAS,CAACkI,KAAK,CAAC,IAAAkG,kCAAa,EAAC,IAAI,EAAE,IAAI,CAACpO,SAAS,CAACC,QAAQ,CAAC,CAAC;IAClE,MAAMoO,MAAM,GAAG,CAAC,IAAI,CAACzG,eAAe,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC5H,SAAS,CAACyI,IAAI,CAAC,CAAC;IACrB,IAAI,IAAI,CAAC6F,qBAAqB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACnR,KAAK,CAACC,kBAAM,CAACmR,yBAAyB,EAAE;QAC3CjR,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACiQ,WAAW,CAAC;MAC7B,CAAC,CAAC;IACJ;IACA,IAAI,CAAC9M,MAAM,GAAS,CAAC;IAErB,OAAO,IAAI,CAAC4I,oBAAoB,CAACrL,IAAI,EAAEoP,MAAM,EAAE,IAAI,CAAC;EACtD;EAIAtD,OAAOA,CAEL9L,IAA4B,EAC5BwP,OAAgB,EACA;IAChB,IAAI,CAAC5L,YAAY,CAAC,eAAe,CAAC;IAClC,IAAI4L,OAAO,EAAE;MACX,IAAI,CAAC5L,YAAY,CAAC,oBAAoB,CAAC;IACzC;IACA5D,IAAI,CAACyP,KAAK,GAAGD,OAAO;IACpB,IAAI,CAACxN,IAAI,CAAC,CAAC;IACX,MAAM0N,SAAS,GAAG,IAAI,CAACpQ,KAAK,CAACqQ,MAAM;IACnC,IAAI,CAACrQ,KAAK,CAACqQ,MAAM,GAAG,EAAE;IACtB,IAAIH,OAAO,EAAE;MAGX,IAAI,CAACzO,SAAS,CAACkI,KAAK,CAAC2G,gCAAW,CAAC;MACjC5P,IAAI,CAACmF,IAAI,GAAG,IAAI,CAAC0K,UAAU,CAAC,CAAC;MAC7B,IAAI,CAAC9O,SAAS,CAACyI,IAAI,CAAC,CAAC;IACvB,CAAC,MAAM;MACLxJ,IAAI,CAACmF,IAAI,GAAG,IAAI,CAAC0K,UAAU,CAAC,CAAC;IAC/B;IAEA,IAAI,CAACvQ,KAAK,CAACqQ,MAAM,GAAGD,SAAS;IAC7B,OAAO,IAAI,CAACpP,UAAU,CAACN,IAAI,EAAE,cAAc,CAAC;EAC9C;EAGA2L,UAAUA,CAAA,EAAY;IACpB,MAAM3L,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAU,CAAC;IACtC,IAAI,CAACpE,IAAI,CAAC,CAAC;IACX,IACE,IAAI,CAAC9C,KAAK,GAAU,CAAC,IACrB,CAAC,IAAI,CAAC4Q,KAAK,CAACC,gBAAgB,IAC5B,CAAC,IAAI,CAACvQ,OAAO,CAACwQ,uBAAuB,EACrC;MACA,IAAI,CAAC9R,KAAK,CAACC,kBAAM,CAAC8R,eAAe,EAAE;QAAE5R,EAAE,EAAE2B;MAAK,CAAC,CAAC;IAClD,CAAC,MAAM,IACL,CAAC,IAAI,CAAC8P,KAAK,CAACI,UAAU,IACtB,CAAC,IAAI,CAAC1Q,OAAO,CAACwQ,uBAAuB,EACrC;MACA,IAAI,CAAC9R,KAAK,CAACC,kBAAM,CAACgS,eAAe,EAAE;QAAE9R,EAAE,EAAE2B;MAAK,CAAC,CAAC;IAClD;IAEA,IACE,CAAC,IAAI,CAACd,KAAK,GAAU,CAAC,IACtB,CAAC,IAAI,CAACA,KAAK,EAAY,CAAC,IACxB,CAAC,IAAI,CAACA,KAAK,GAAO,CAAC,EACnB;MACA,IAAI,CAAChB,KAAK,CAACC,kBAAM,CAACiS,gBAAgB,EAAE;QAAE/R,EAAE,EAAE2B;MAAK,CAAC,CAAC;IACnD;IAEA,OAAO,IAAI,CAACM,UAAU,CAACN,IAAI,EAAE,OAAO,CAAC;EACvC;EAEA4C,gBAAgBA,CAAA,EAAkB;IAChC,MAAM5C,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAgB,CAAC;IAC5C,MAAMyH,EAAE,GAAG,IAAI,CAAC5N,WAAW,CAGzB,IAAAoO,wCAA8B,EAAC,IAAI,CAAC/O,KAAK,CAACQ,QAAQ,EAAE,CAAC,CACvD,CAAC;IACD,MAAM9B,IAAI,GAAG,IAAI,CAACsB,KAAK,CAACrB,KAAK;IAC7B,IAAI,CAAC+D,IAAI,CAAC,CAAC;IACXhC,IAAI,CAAC6N,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAACxC,EAAE,EAAE7P,IAAI,CAAC;IACzC,OAAO,IAAI,CAACsC,UAAU,CAACN,IAAI,EAAE,aAAa,CAAC;EAC7C;EAEA2M,2BAA2BA,CAAA,EAEc;IACvC,MAAM3M,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAwC,CAAC;IAOpE,IAAI,CAACpE,IAAI,CAAC,CAAC;IAEX,IAAI,IAAI,CAACjB,SAAS,CAACC,QAAQ,IAAI,IAAI,CAAC9B,KAAK,GAAO,CAAC,EAAE;MACjD,MAAMoR,IAAI,GAAG,IAAI,CAACD,gBAAgB,CAChC,IAAI,CAACtC,eAAe,CAAe/N,IAAI,CAAC,EACxC,UACF,CAAC;MACD,IAAI,CAACgC,IAAI,CAAC,CAAC;MAEX,IAAI,IAAI,CAAC9C,KAAK,IAAS,CAAC,EAAE;QACxB,IAAI,CAAC0E,YAAY,CAAC,cAAc,CAAC;MACnC,CAAC,MAAM,IAAI,CAAC,IAAI,CAACK,SAAS,CAAC,cAAc,CAAC,EAAE;QAE1C,IAAI,CAAC9E,UAAU,CAAC,CAAC;MACnB;MACA,OAAO,IAAI,CAACoR,iBAAiB,CAC3BvQ,IAAI,EACJsQ,IAAI,EACJ,MACF,CAAC;IACH;IACA,OAAO,IAAI,CAACE,aAAa,CAACxQ,IAAoC,CAAC;EACjE;EAEAuQ,iBAAiBA,CACfvQ,IAA4B,EAC5BsQ,IAAkB,EAClBG,YAAoB,EACJ;IAChBzQ,IAAI,CAACsQ,IAAI,GAAGA,IAAI;IAEhB,MAAM1C,WAAW,GAAG,IAAI,CAACtO,KAAK,CAACsO,WAAW;IAE1C5N,IAAI,CAACyI,QAAQ,GAAG,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC;IAE1C,IAAI3I,IAAI,CAACyI,QAAQ,CAACzK,IAAI,KAAKyS,YAAY,IAAI7C,WAAW,EAAE;MACtD,IAAI,CAAC1P,KAAK,CAACC,kBAAM,CAACuS,uBAAuB,EAAE;QACzCrS,EAAE,EAAE2B,IAAI,CAACyI,QAAQ;QACjBkI,MAAM,EAAEL,IAAI,CAACtS,IAAI;QACjB4S,qBAAqB,EAAEH;MACzB,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI,CAACnQ,UAAU,CAACN,IAAI,EAAE,cAAc,CAAC;EAC9C;EAGA4L,uBAAuBA,CAAC5L,IAA4B,EAAkB;IACpE,MAAM6N,EAAE,GAAG,IAAI,CAACwC,gBAAgB,CAC9B,IAAI,CAACtC,eAAe,CAAe/N,IAAI,CAAC,EACxC,QACF,CAAC;IACD,IAAI,CAACgC,IAAI,CAAC,CAAC;IAEX,IAAI,IAAI,CAAClB,YAAY,IAAS,CAAC,EAAE;MAC/B,IAAI,CAAC,IAAI,CAAC+P,QAAQ,EAAE;QAClB,IAAI,CAAC3S,KAAK,CAACC,kBAAM,CAAC2S,uBAAuB,EAAE;UAAEzS,EAAE,EAAEwP;QAAG,CAAC,CAAC;MACxD;MACA,IAAI,CAACkD,iBAAiB,GAAG,IAAI;IAC/B;IAEA,OAAO,IAAI,CAACR,iBAAiB,CAACvQ,IAAI,EAAE6N,EAAE,EAAE,MAAM,CAAC;EACjD;EAEAmD,kBAAkBA,CAChB/S,KAAU,EACVN,IAAe,EACfqC,IAAS,EACN;IACH,IAAI,CAACiR,QAAQ,CAACjR,IAAI,EAAE,UAAU,EAAE/B,KAAK,CAAC;IACtC,IAAI,CAACgT,QAAQ,CAACjR,IAAI,EAAE,KAAK,EAAE,IAAI,CAACqN,KAAK,CAAC6D,KAAK,CAAClR,IAAI,CAACvB,KAAK,EAAE,IAAI,CAACa,KAAK,CAAC4K,GAAG,CAAC,CAAC;IACxElK,IAAI,CAAC/B,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+D,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC1B,UAAU,CAAIN,IAAI,EAAErC,IAAI,CAAC;EACvC;EAEAwT,YAAYA,CAAmBlT,KAAU,EAAEN,IAAe,EAAK;IAC7D,MAAMqC,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAC,CAAC;IAC7B,OAAO,IAAI,CAAC4K,kBAAkB,CAAC/S,KAAK,EAAEN,IAAI,EAAEqC,IAAI,CAAC;EACnD;EAEAoM,kBAAkBA,CAACnO,KAAU,EAAE;IAC7B,OAAO,IAAI,CAACkT,YAAY,CAAkBlT,KAAK,EAAE,eAAe,CAAC;EACnE;EAEAgO,mBAAmBA,CAAChO,KAAU,EAAE;IAC9B,OAAO,IAAI,CAACkT,YAAY,CAAmBlT,KAAK,EAAE,gBAAgB,CAAC;EACrE;EAEAiO,kBAAkBA,CAACjO,KAAU,EAAE;IAC7B,OAAO,IAAI,CAACkT,YAAY,CAAkBlT,KAAK,EAAE,eAAe,CAAC;EACnE;EAEAkO,mBAAmBA,CAAClO,KAAU,EAAE;IAC9B,OAAO,IAAI,CAACkT,YAAY,CAAmBlT,KAAK,EAAE,gBAAgB,CAAC;EACrE;EAEA+N,kBAAkBA,CAAC/N,KAIlB,EAAE;IACD,MAAM+B,IAAI,GAAG,IAAI,CAACmR,YAAY,CAC5BlT,KAAK,CAACA,KAAK,EACX,eACF,CAAC;IACD+B,IAAI,CAACoR,OAAO,GAAGnT,KAAK,CAACmT,OAAO;IAC5BpR,IAAI,CAACqR,KAAK,GAAGpT,KAAK,CAACoT,KAAK;IACxB,OAAOrR,IAAI;EACb;EAEAsM,mBAAmBA,CAACrO,KAAc,EAAE;IAClC,MAAM+B,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAmB,CAAC;IAC/CpG,IAAI,CAAC/B,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+D,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC1B,UAAU,CAACN,IAAI,EAAE,gBAAgB,CAAC;EAChD;EAEAqM,gBAAgBA,CAAA,EAAG;IACjB,MAAMrM,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAgB,CAAC;IAC5C,IAAI,CAACpE,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAAC1B,UAAU,CAACN,IAAI,EAAE,aAAa,CAAC;EAC7C;EAGAwM,kCAAkCA,CAEhCD,UAAmB,EACL;IACd,MAAMzM,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IAEpC,IAAIwR,GAAG;IACP,IAAI,CAACtP,IAAI,CAAC,CAAC;IACX,IAAI,CAACgH,eAAe,CAACC,KAAK,CAAC,IAAAsI,kCAAiB,EAAC,CAAC,CAAC;IAE/C,MAAMzI,yBAAyB,GAAG,IAAI,CAACxJ,KAAK,CAACyJ,sBAAsB;IACnE,MAAM+B,6BAA6B,GAAG,IAAI,CAACxL,KAAK,CAACuE,0BAA0B;IAC3E,IAAI,CAACvE,KAAK,CAACyJ,sBAAsB,GAAG,IAAI;IACxC,IAAI,CAACzJ,KAAK,CAACuE,0BAA0B,GAAG,KAAK;IAE7C,MAAM2N,aAAa,GAAG,IAAI,CAAClS,KAAK,CAACQ,QAAQ;IACzC,MAAM2R,QAAwB,GAAG,EAAE;IACnC,MAAM/T,mBAAmB,GAAG,IAAI2D,sBAAgB,CAAC,CAAC;IAClD,IAAIwJ,KAAK,GAAG,IAAI;IAChB,IAAI6G,cAAc;IAClB,IAAIC,qBAAqB;IAEzB,OAAO,CAAC,IAAI,CAACzS,KAAK,GAAU,CAAC,EAAE;MAC7B,IAAI2L,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACpI,MAAM,KAET/E,mBAAmB,CAACmD,qBAAqB,KAAK,IAAI,GAC9C,IAAI,GACJnD,mBAAmB,CAACmD,qBAC1B,CAAC;QACD,IAAI,IAAI,CAAC3B,KAAK,GAAU,CAAC,EAAE;UACzByS,qBAAqB,GAAG,IAAI,CAACrS,KAAK,CAACQ,QAAQ;UAC3C;QACF;MACF;MAEA,IAAI,IAAI,CAACZ,KAAK,GAAY,CAAC,EAAE;QAC3B,MAAM0S,kBAAkB,GAAG,IAAI,CAACtS,KAAK,CAACQ,QAAQ;QAC9C4R,cAAc,GAAG,IAAI,CAACpS,KAAK,CAACQ,QAAQ;QACpC2R,QAAQ,CAACrR,IAAI,CACX,IAAI,CAACyR,cAAc,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAEF,kBAAkB,CACjE,CAAC;QAED,IAAI,CAAC,IAAI,CAACG,mBAAmB,GAA2B,CAAC,EAAE;UACzD;QACF;MACF,CAAC,MAAM;QACLN,QAAQ,CAACrR,IAAI,CACX,IAAI,CAACK,uBAAuB,CAC1B/C,mBAAmB,EACnB,IAAI,CAACmU,cACP,CACF,CAAC;MACH;IACF;IAEA,MAAMG,WAAW,GAAG,IAAI,CAAC1S,KAAK,CAAC2K,aAAa;IAC5C,IAAI,CAACxH,MAAM,GAAU,CAAC;IAEtB,IAAI,CAACnD,KAAK,CAACyJ,sBAAsB,GAAGD,yBAAyB;IAC7D,IAAI,CAACxJ,KAAK,CAACuE,0BAA0B,GAAGiH,6BAA6B;IAErE,IAAImH,SAAS,GAAG,IAAI,CAAChS,WAAW,CAA4BH,QAAQ,CAAC;IACrE,IACEyM,UAAU,IACV,IAAI,CAAC2F,gBAAgB,CAACT,QAAQ,CAAC,KAC9BQ,SAAS,GAAG,IAAI,CAACE,UAAU,CAACF,SAAS,CAAC,CAAC,EACxC;MACA,IAAI,CAAClQ,yBAAyB,CAACrE,mBAAmB,CAAC;MACnD,IAAI,CAACsL,eAAe,CAACO,iBAAiB,CAAC,CAAC;MACxC,IAAI,CAACP,eAAe,CAACQ,IAAI,CAAC,CAAC;MAC3B,IAAI,CAAC6B,oBAAoB,CAAC4G,SAAS,EAAER,QAAQ,EAAE,KAAK,CAAC;MAErD,OAAOQ,SAAS;IAClB;IACA,IAAI,CAACjJ,eAAe,CAACQ,IAAI,CAAC,CAAC;IAE3B,IAAI,CAACiI,QAAQ,CAACrH,MAAM,EAAE;MACpB,IAAI,CAACjL,UAAU,CAAC,IAAI,CAACG,KAAK,CAAC0L,eAAe,CAAC;IAC7C;IACA,IAAI2G,qBAAqB,EAAE,IAAI,CAACxS,UAAU,CAACwS,qBAAqB,CAAC;IACjE,IAAID,cAAc,EAAE,IAAI,CAACvS,UAAU,CAACuS,cAAc,CAAC;IACnD,IAAI,CAACtP,qBAAqB,CAAC1E,mBAAmB,EAAE,IAAI,CAAC;IAErD,IAAI,CAACkM,oBAAoB,CAAC6H,QAAQ,EAA4B,IAAI,CAAC;IACnE,IAAIA,QAAQ,CAACrH,MAAM,GAAG,CAAC,EAAE;MACvBkH,GAAG,GAAG,IAAI,CAACrR,WAAW,CAAuBuR,aAAa,CAAC;MAC3DF,GAAG,CAACpR,WAAW,GAAGuR,QAAQ;MAE1B,IAAI,CAACnR,UAAU,CAACgR,GAAG,EAAE,oBAAoB,CAAC;MAC1C,IAAI,CAACc,gBAAgB,CAACd,GAAG,EAAEU,WAAW,CAAC;IACzC,CAAC,MAAM;MACLV,GAAG,GAAGG,QAAQ,CAAC,CAAC,CAAC;IACnB;IAEA,OAAO,IAAI,CAACY,eAAe,CACzBvS,QAAQ,EAERwR,GACF,CAAC;EACH;EAEAe,eAAeA,CAACvS,QAAkB,EAAEwS,UAAwB,EAAgB;IAC1E,IAAI,CAAC,IAAI,CAAC9S,OAAO,CAAC+S,8BAA8B,EAAE;MAChD,IAAI,CAACtB,QAAQ,CAACqB,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC;MAChD,IAAI,CAACrB,QAAQ,CAACqB,UAAU,EAAE,YAAY,EAAExS,QAAQ,CAAC8B,KAAK,CAAC;MAEvD,IAAI,CAAC4Q,uBAAuB,CAC1BF,UAAU,EACVxS,QAAQ,CAAC8B,KAAK,EACd,IAAI,CAACtC,KAAK,CAAC2K,aAAa,CAACrI,KAC3B,CAAC;MAED,OAAO0Q,UAAU;IACnB;IAEA,MAAMG,eAAe,GACnB,IAAI,CAACxS,WAAW,CAA4BH,QAAQ,CAAC;IACvD2S,eAAe,CAACH,UAAU,GAAGA,UAAU;IACvC,OAAO,IAAI,CAAChS,UAAU,CAACmS,eAAe,EAAE,yBAAyB,CAAC;EACpE;EAGAP,gBAAgBA,CAAC9C,MAAqB,EAAW;IAC/C,OAAO,CAAC,IAAI,CAAC9H,kBAAkB,CAAC,CAAC;EACnC;EAEA6K,UAAUA,CACRnS,IAAuC,EACQ;IAC/C,IAAI,IAAI,CAACG,GAAG,GAAS,CAAC,EAAE;MACtB,OAAOH,IAAI;IACb;EACF;EAEA6R,cAAcA,CACZ7R,IAAkB,EAElBF,QAAkB,EACJ;IACd,OAAOE,IAAI;EACb;EAEA+M,mBAAmBA,CAAA,EAAiD;IAClE,MAAM/M,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAmC,CAAC;IAC/D,IAAI,CAACpE,IAAI,CAAC,CAAC;IACX,IAAI,IAAI,CAAC9C,KAAK,GAAO,CAAC,EAAE;MAEtB,MAAMoR,IAAI,GAAG,IAAI,CAACD,gBAAgB,CAChC,IAAI,CAACtC,eAAe,CAAe/N,IAAI,CAAC,EACxC,KACF,CAAC;MACD,IAAI,CAACgC,IAAI,CAAC,CAAC;MACX,MAAM0Q,QAAQ,GAAG,IAAI,CAACnC,iBAAiB,CACrCvQ,IAAI,EACJsQ,IAAI,EACJ,QACF,CAAC;MAED,IACE,CAAC,IAAI,CAACR,KAAK,CAAC6C,kBAAkB,IAC9B,CAAC,IAAI,CAAC7C,KAAK,CAAC8C,OAAO,IACnB,CAAC,IAAI,CAACpT,OAAO,CAACqT,6BAA6B,EAC3C;QACA,IAAI,CAAC3U,KAAK,CAACC,kBAAM,CAAC2U,mBAAmB,EAAE;UAAEzU,EAAE,EAAEqU;QAAS,CAAC,CAAC;MAC1D;MAEA,OAAOA,QAAQ;IACjB;IAEA,OAAO,IAAI,CAACK,QAAQ,CAAC/S,IAA+B,CAAC;EACvD;EAQA+S,QAAQA,CAAe/S,IAA6B,EAAmB;IACrE,IAAI,CAACgT,cAAc,CAAChT,IAAI,CAAC;IAEzB,IAAI,IAAI,CAACG,GAAG,GAAU,CAAC,EAAE;MACvB,MAAM8S,IAAI,GAAG,IAAI,CAACC,aAAa,GAAU,CAAC;MAC1C,IAAI,CAAC7S,gBAAgB,CAAC4S,IAAI,CAAC;MAE3BjT,IAAI,CAACmJ,SAAS,GAAG8J,IAAI;IACvB,CAAC,MAAM;MACLjT,IAAI,CAACmJ,SAAS,GAAG,EAAE;IACrB;IAEA,OAAO,IAAI,CAAC7I,UAAU,CAACN,IAAI,EAAE,eAAe,CAAC;EAC/C;EAEAgT,cAAcA,CAAehT,IAA6B,EAAQ;IAChEA,IAAI,CAAC4I,MAAM,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACpC,IAAI7I,IAAI,CAAC4I,MAAM,CAACjL,IAAI,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACO,KAAK,CAACC,kBAAM,CAACgV,0BAA0B,EAAE;QAAE9U,EAAE,EAAE2B,IAAI,CAAC4I;MAAO,CAAC,CAAC;IACpE;EACF;EAIAwK,oBAAoBA,CAACC,QAAiB,EAAqB;IACzD,MAAM;MAAE5U,KAAK;MAAEqB,QAAQ;MAAEoK,GAAG;MAAEjM;IAAM,CAAC,GAAG,IAAI,CAACqB,KAAK;IAClD,MAAMgU,SAAS,GAAG7U,KAAK,GAAG,CAAC;IAC3B,MAAM8U,IAAI,GAAG,IAAI,CAACtT,WAAW,CAC3B,IAAAoO,wCAA8B,EAACvO,QAAQ,EAAE,CAAC,CAC5C,CAAC;IACD,IAAI7B,KAAK,KAAK,IAAI,EAAE;MAClB,IAAI,CAACoV,QAAQ,EAAE;QACb,IAAI,CAACnV,KAAK,CAACC,kBAAM,CAACqV,6BAA6B,EAAE;UAE/CnV,EAAE,EAAE,IAAAgQ,wCAA8B,EAChC,IAAI,CAAC/O,KAAK,CAACmU,6BAA6B,EACxC,CACF;QACF,CAAC,CAAC;MACJ;IACF;IAEA,MAAMC,MAAM,GAAG,IAAI,CAACxU,KAAK,GAAgB,CAAC;IAC1C,MAAMyU,SAAS,GAAGD,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAClC,MAAME,OAAO,GAAG1J,GAAG,GAAGyJ,SAAS;IAC/BJ,IAAI,CAACtV,KAAK,GAAG;MACX4V,GAAG,EAAE,IAAI,CAACxG,KAAK,CAAC6D,KAAK,CAACoC,SAAS,EAAEM,OAAO,CAAC,CAACE,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;MACjEC,MAAM,EAAE9V,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGA,KAAK,CAACiT,KAAK,CAAC,CAAC,EAAEyC,SAAS;IAC1D,CAAC;IACDJ,IAAI,CAACS,IAAI,GAAGN,MAAM;IAClB,IAAI,CAAC1R,IAAI,CAAC,CAAC;IACX,MAAMsC,YAAY,GAAG,IAAI,CAAChE,UAAU,CAACiT,IAAI,EAAE,iBAAiB,CAAC;IAC7D,IAAI,CAACnB,gBAAgB,CACnB9N,YAAY,EACZ,IAAA+J,wCAA8B,EAAC,IAAI,CAAC/O,KAAK,CAAC2K,aAAa,EAAE0J,SAAS,CACpE,CAAC;IACD,OAAOrP,YAAY;EACrB;EAGAyF,aAAaA,CAAesJ,QAAiB,EAAqB;IAChE,MAAMrT,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAoB,CAAC;IAChDpG,IAAI,CAACE,WAAW,GAAG,EAAE;IACrB,IAAI+T,MAAM,GAAG,IAAI,CAACb,oBAAoB,CAACC,QAAQ,CAAC;IAChDrT,IAAI,CAACkU,MAAM,GAAG,CAACD,MAAM,CAAC;IACtB,OAAO,CAACA,MAAM,CAACD,IAAI,EAAE;MACnBhU,IAAI,CAACE,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC+T,yBAAyB,CAAC,CAAC,CAAC;MACvD,IAAI,CAACC,wBAAwB,CAAC,CAAC;MAC/BpU,IAAI,CAACkU,MAAM,CAAC9T,IAAI,CAAE6T,MAAM,GAAG,IAAI,CAACb,oBAAoB,CAACC,QAAQ,CAAE,CAAC;IAClE;IACA,OAAO,IAAI,CAAC/S,UAAU,CAACN,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAGAmU,yBAAyBA,CAAA,EAA6B;IACpD,OAAO,IAAI,CAAClV,eAAe,CAAC,CAAC;EAC/B;EAsBAyN,eAAeA,CAEblC,KAAgB,EAChB6J,SAAkB,EAClB7W,QAAyB,EACzBE,mBAA6C,EAC1C;IACH,IAAIF,QAAQ,EAAE;MACZ,IAAI,CAACoG,YAAY,CAAC,gBAAgB,CAAC;IACrC;IACA,MAAMkH,6BAA6B,GAAG,IAAI,CAACxL,KAAK,CAACuE,0BAA0B;IAC3E,IAAI,CAACvE,KAAK,CAACuE,0BAA0B,GAAG,KAAK;IAC7C,MAAMyQ,QAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI3J,KAAK,GAAG,IAAI;IAChB,MAAM7K,IAAI,GAAG,IAAI,CAACoG,SAAS,CAEzB,CAAC;IAEHpG,IAAI,CAACyU,UAAU,GAAG,EAAE;IACpB,IAAI,CAACzS,IAAI,CAAC,CAAC;IAEX,OAAO,CAAC,IAAI,CAAC9C,KAAK,CAACsL,KAAK,CAAC,EAAE;MACzB,IAAIK,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACpI,MAAM,GAAS,CAAC;QACrB,IAAI,IAAI,CAACvD,KAAK,CAACsL,KAAK,CAAC,EAAE;UACrB,IAAI,CAACS,2BAA2B,CAE9BjL,IACF,CAAC;UACD;QACF;MACF;MAEA,IAAIzC,IAAI;MACR,IAAI8W,SAAS,EAAE;QACb9W,IAAI,GAAG,IAAI,CAACmX,oBAAoB,CAAC,CAAC;MACpC,CAAC,MAAM;QACLnX,IAAI,GAAG,IAAI,CAACoX,uBAAuB,CAACjX,mBAAmB,CAAC;QACxD,IAAI,CAACJ,UAAU,CAACC,IAAI,EAAEC,QAAQ,EAAE8W,QAAQ,EAAE5W,mBAAmB,CAAC;MAChE;MAEA,IACEF,QAAQ,IACR,CAAC,IAAI,CAACoX,gBAAgB,CAACrX,IAAI,CAAC,IAC5BA,IAAI,CAACI,IAAI,KAAK,eAAe,EAC7B;QACA,IAAI,CAACO,KAAK,CAACC,kBAAM,CAAC0W,qBAAqB,EAAE;UAAExW,EAAE,EAAEd;QAAK,CAAC,CAAC;MACxD;MAGA,IAAIA,IAAI,CAACO,SAAS,EAAE;QAClB,IAAI,CAACmT,QAAQ,CAAC1T,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC;MACxC;MAGAyC,IAAI,CAACyU,UAAU,CAACrU,IAAI,CAAC7C,IAAI,CAAC;IAC5B;IAEA,IAAI,CAACyE,IAAI,CAAC,CAAC;IAEX,IAAI,CAAC1C,KAAK,CAACuE,0BAA0B,GAAGiH,6BAA6B;IACrE,IAAInN,IAAI,GAAG,kBAAkB;IAC7B,IAAI0W,SAAS,EAAE;MACb1W,IAAI,GAAG,eAAe;IACxB,CAAC,MAAM,IAAIH,QAAQ,EAAE;MACnBG,IAAI,GAAG,kBAAkB;IAC3B;IAEA,OAAO,IAAI,CAAC2C,UAAU,CAACN,IAAI,EAAErC,IAAI,CAAC;EACpC;EAEAsN,2BAA2BA,CAACjL,IAAY,EAAQ;IAC9C,IAAI,CAACiR,QAAQ,CAACjR,IAAI,EAAE,eAAe,EAAE,IAAI,CAACV,KAAK,CAACwV,YAAY,CAAC;IAC7D,IAAI,CAAC7D,QAAQ,CAACjR,IAAI,EAAE,kBAAkB,EAAE,IAAI,CAACV,KAAK,CAAC0L,eAAe,EAAE,KAAK,CAAC;EAC5E;EAKA+J,wBAAwBA,CAACxX,IAA8B,EAAW;IAChE,OACE,CAACA,IAAI,CAACM,QAAQ,IACdN,IAAI,CAACQ,GAAG,CAACJ,IAAI,KAAK,YAAY,KAC7B,IAAI,CAACqX,qBAAqB,CAAC,CAAC,IAC3B,IAAI,CAAC9V,KAAK,EAAY,CAAC,IACvB,IAAI,CAACA,KAAK,GAAQ,CAAC,CAAC;EAE1B;EAGAyV,uBAAuBA,CAErBjX,mBAA6C,EACX;IAClC,IAAIgO,UAAU,GAAG,EAAE;IACnB,IAAI,IAAI,CAACxM,KAAK,GAAM,CAAC,EAAE;MACrB,IAAI,IAAI,CAAC+E,SAAS,CAAC,YAAY,CAAC,EAAE;QAChC,IAAI,CAAC/F,KAAK,CAACC,kBAAM,CAAC8W,4BAA4B,EAAE;UAC9C5W,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;QACjB,CAAC,CAAC;MACJ;MAIA,OAAO,IAAI,CAACZ,KAAK,GAAM,CAAC,EAAE;QACxBwM,UAAU,CAACtL,IAAI,CAAC,IAAI,CAAC8U,cAAc,CAAC,CAAC,CAAC;MACxC;IACF;IAEA,MAAM3X,IAAI,GAAG,IAAI,CAAC6I,SAAS,CAAmB,CAAC;IAC/C,IAAIoJ,OAAO,GAAG,KAAK;IACnB,IAAI2F,UAAU,GAAG,KAAK;IACtB,IAAIrV,QAAQ;IAEZ,IAAI,IAAI,CAACZ,KAAK,GAAY,CAAC,EAAE;MAC3B,IAAIwM,UAAU,CAACtB,MAAM,EAAE,IAAI,CAACjL,UAAU,CAAC,CAAC;MACxC,OAAO,IAAI,CAACiW,WAAW,CAAC,CAAC;IAC3B;IAEA,IAAI1J,UAAU,CAACtB,MAAM,EAAE;MACrB7M,IAAI,CAACmO,UAAU,GAAGA,UAAU;MAC5BA,UAAU,GAAG,EAAE;IACjB;IAEAnO,IAAI,CAAC8X,MAAM,GAAG,KAAK;IAEnB,IAAI3X,mBAAmB,EAAE;MACvBoC,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IAChC;IAEA,IAAIwV,WAAW,GAAG,IAAI,CAACnV,GAAG,GAAQ,CAAC;IACnC,IAAI,CAACoV,+BAA+B,CAAChY,IAAI,CAAC;IAC1C,MAAMqQ,WAAW,GAAG,IAAI,CAACtO,KAAK,CAACsO,WAAW;IAC1C,MAAM7P,GAAG,GAAG,IAAI,CAACyX,iBAAiB,CAACjY,IAAI,EAAEG,mBAAmB,CAAC;IAE7D,IAAI,CAAC4X,WAAW,IAAI,CAAC1H,WAAW,IAAI,IAAI,CAACmH,wBAAwB,CAACxX,IAAI,CAAC,EAAE;MACvE,MAAMkY,OAAO,GAAG1X,GAAG,CAACC,IAAI;MAGxB,IAAIyX,OAAO,KAAK,OAAO,IAAI,CAAC,IAAI,CAACpG,qBAAqB,CAAC,CAAC,EAAE;QACxDG,OAAO,GAAG,IAAI;QACd,IAAI,CAACpE,iCAAiC,CAACrN,GAAG,CAAC;QAC3CuX,WAAW,GAAG,IAAI,CAACnV,GAAG,GAAQ,CAAC;QAC/B,IAAI,CAACqV,iBAAiB,CAACjY,IAAI,CAAC;MAC9B;MAGA,IAAIkY,OAAO,KAAK,KAAK,IAAIA,OAAO,KAAK,KAAK,EAAE;QAC1CN,UAAU,GAAG,IAAI;QACjB,IAAI,CAAC/J,iCAAiC,CAACrN,GAAG,CAAC;QAC3CR,IAAI,CAACmY,IAAI,GAAGD,OAAO;QACnB,IAAI,IAAI,CAACvW,KAAK,GAAQ,CAAC,EAAE;UACvBoW,WAAW,GAAG,IAAI;UAClB,IAAI,CAACpX,KAAK,CAACC,kBAAM,CAACwX,mBAAmB,EAAE;YACrCtX,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACiQ,WAAW,CAAC,CAAC;YAC5BmG,IAAI,EAAED;UACR,CAAC,CAAC;UACF,IAAI,CAACzT,IAAI,CAAC,CAAC;QACb;QACA,IAAI,CAACwT,iBAAiB,CAACjY,IAAI,CAAC;MAC9B;IACF;IAEA,OAAO,IAAI,CAACqY,iBAAiB,CAC3BrY,IAAI,EACJuC,QAAQ,EACRwV,WAAW,EACX9F,OAAO,EACP,KAAK,EACL2F,UAAU,EACVzX,mBACF,CAAC;EACH;EAEAmY,iCAAiCA,CAC/BR,MAAsC,EAC9B;IACR,OAAOA,MAAM,CAACK,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC;EACtC;EAGAI,4BAA4BA,CAACT,MAAsC,EAAE;IACnE,OAAOA,MAAM,CAACjG,MAAM;EACtB;EAIA2G,uBAAuBA,CAACV,MAAsC,EAAQ;IAAA,IAAAW,OAAA;IACpE,MAAMC,UAAU,GAAG,IAAI,CAACJ,iCAAiC,CAACR,MAAM,CAAC;IACjE,MAAMjG,MAAM,GAAG,IAAI,CAAC0G,4BAA4B,CAACT,MAAM,CAAC;IAExD,IAAIjG,MAAM,CAAChF,MAAM,KAAK6L,UAAU,EAAE;MAChC,IAAI,CAAC/X,KAAK,CACRmX,MAAM,CAACK,IAAI,KAAK,KAAK,GAAGvX,kBAAM,CAAC+X,cAAc,GAAG/X,kBAAM,CAACgY,cAAc,EACrE;QAAE9X,EAAE,EAAEgX;MAAO,CACf,CAAC;IACH;IAEA,IACEA,MAAM,CAACK,IAAI,KAAK,KAAK,IACrB,EAAAM,OAAA,GAAA5G,MAAM,CAACA,MAAM,CAAChF,MAAM,GAAG,CAAC,CAAC,qBAAzB4L,OAAA,CAA2BrY,IAAI,MAAK,aAAa,EACjD;MACA,IAAI,CAACO,KAAK,CAACC,kBAAM,CAACiY,sBAAsB,EAAE;QAAE/X,EAAE,EAAEgX;MAAO,CAAC,CAAC;IAC3D;EACF;EAGAgB,iBAAiBA,CAEf9Y,IAA4B,EAC5B+X,WAAoB,EACpB9F,OAAgB,EAChB6E,SAAkB,EAClBc,UAAmB,EACgB;IACnC,IAAIA,UAAU,EAAE;MAEd,MAAMmB,YAAY,GAAG,IAAI,CAACC,WAAW,CACnChZ,IAAI,EAGJ+X,WAAW,EACG,KAAK,EACC,KAAK,EACzB,KAAK,EACL,cACF,CAAC;MACD,IAAI,CAACS,uBAAuB,CAACO,YAAY,CAAC;MAC1C,OAAOA,YAAY;IACrB;IAEA,IAAI9G,OAAO,IAAI8F,WAAW,IAAI,IAAI,CAACpW,KAAK,GAAU,CAAC,EAAE;MACnD,IAAImV,SAAS,EAAE,IAAI,CAAClV,UAAU,CAAC,CAAC;MAChC5B,IAAI,CAACmY,IAAI,GAAG,QAAQ;MACpBnY,IAAI,CAAC8X,MAAM,GAAG,IAAI;MAClB,OAAO,IAAI,CAACkB,WAAW,CACrBhZ,IAAI,EACJ+X,WAAW,EACX9F,OAAO,EACa,KAAK,EACzB,KAAK,EACL,cACF,CAAC;IACH;EACF;EAIAgH,mBAAmBA,CAEjBjZ,IAA8B,EAC9BuC,QAAqC,EACrCuU,SAAkB,EAClB3W,mBAA6C,EACR;IACrCH,IAAI,CAACO,SAAS,GAAG,KAAK;IAEtB,IAAI,IAAI,CAACqC,GAAG,GAAS,CAAC,EAAE;MACtB5C,IAAI,CAACU,KAAK,GAAGoW,SAAS,GAClB,IAAI,CAACoC,iBAAiB,CAAC,IAAI,CAACnX,KAAK,CAACQ,QAAQ,CAAC,GAC3C,IAAI,CAACW,uBAAuB,CAAC/C,mBAAmB,CAAC;MAErD,OAAO,IAAI,CAAC4C,UAAU,CAAC/C,IAAI,EAAE,gBAAgB,CAAC;IAChD;IAEA,IAAI,CAACA,IAAI,CAACM,QAAQ,IAAIN,IAAI,CAACQ,GAAG,CAACJ,IAAI,KAAK,YAAY,EAAE;MAKpD,IAAI,CAAC+Y,iBAAiB,CAACnZ,IAAI,CAACQ,GAAG,CAACC,IAAI,EAAET,IAAI,CAACQ,GAAG,CAACS,GAAG,CAACC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;MAEtE,IAAI4V,SAAS,EAAE;QACb9W,IAAI,CAACU,KAAK,GAAG,IAAI,CAACwY,iBAAiB,CACjC3W,QAAQ,EACR,IAAA6W,qBAAe,EAACpZ,IAAI,CAACQ,GAAG,CAC1B,CAAC;MACH,CAAC,MAAM,IAAI,IAAI,CAACmB,KAAK,GAAM,CAAC,EAAE;QAC5B,MAAM2C,kBAAkB,GAAG,IAAI,CAACvC,KAAK,CAACQ,QAAQ;QAC9C,IAAIpC,mBAAmB,IAAI,IAAI,EAAE;UAC/B,IAAIA,mBAAmB,CAACmE,kBAAkB,KAAK,IAAI,EAAE;YACnDnE,mBAAmB,CAACmE,kBAAkB,GAAGA,kBAAkB;UAC7D;QACF,CAAC,MAAM;UACL,IAAI,CAAC3D,KAAK,CAACC,kBAAM,CAACyY,2BAA2B,EAAE;YAC7CvY,EAAE,EAAEwD;UACN,CAAC,CAAC;QACJ;QACAtE,IAAI,CAACU,KAAK,GAAG,IAAI,CAACwY,iBAAiB,CACjC3W,QAAQ,EACR,IAAA6W,qBAAe,EAACpZ,IAAI,CAACQ,GAAG,CAC1B,CAAC;MACH,CAAC,MAAM;QACLR,IAAI,CAACU,KAAK,GAAG,IAAA0Y,qBAAe,EAACpZ,IAAI,CAACQ,GAAG,CAAC;MACxC;MACAR,IAAI,CAACO,SAAS,GAAG,IAAI;MAErB,OAAO,IAAI,CAACwC,UAAU,CAAC/C,IAAI,EAAE,gBAAgB,CAAC;IAChD;EACF;EAEAqY,iBAAiBA,CAEfrY,IAA+C,EAC/CuC,QAAqC,EACrCwV,WAAoB,EACpB9F,OAAgB,EAChB6E,SAAkB,EAClBc,UAAmB,EACnBzX,mBAA6C,EACV;IACnC,MAAMsC,IAAI,GACR,IAAI,CAACqW,iBAAiB,CACpB9Y,IAAI,EACJ+X,WAAW,EACX9F,OAAO,EACP6E,SAAS,EACTc,UACF,CAAC,IACD,IAAI,CAACqB,mBAAmB,CACtBjZ,IAAI,EACJuC,QAAQ,EACRuU,SAAS,EACT3W,mBACF,CAAC;IAEH,IAAI,CAACsC,IAAI,EAAE,IAAI,CAACb,UAAU,CAAC,CAAC;IAE5B,OAAOa,IAAI;EACb;EAKAwV,iBAAiBA,CAEfjY,IAE4B,EAC5BG,mBAA6C,EAChB;IAC7B,IAAI,IAAI,CAACyC,GAAG,EAAY,CAAC,EAAE;MACxB5C,IAAI,CAAmCM,QAAQ,GAAG,IAAI;MACvDN,IAAI,CAACQ,GAAG,GAAG,IAAI,CAAC0C,uBAAuB,CAAC,CAAC;MACzC,IAAI,CAACgC,MAAM,EAAY,CAAC;IAC1B,CAAC,MAAM;MAEL,MAAM;QAAE9E,IAAI;QAAEM;MAAM,CAAC,GAAG,IAAI,CAACqB,KAAK;MAClC,IAAIvB,GAAG;MAEP,IAAI,IAAA8Y,iCAA0B,EAAClZ,IAAI,CAAC,EAAE;QACpCI,GAAG,GAAG,IAAI,CAAC4K,eAAe,CAAC,IAAI,CAAC;MAClC,CAAC,MAAM;QACL,QAAQhL,IAAI;UACV;YACEI,GAAG,GAAG,IAAI,CAACkO,mBAAmB,CAAChO,KAAK,CAAC;YACrC;UACF;YACEF,GAAG,GAAG,IAAI,CAACqO,kBAAkB,CAACnO,KAAK,CAAC;YACpC;UACF;YACEF,GAAG,GAAG,IAAI,CAACmO,kBAAkB,CAACjO,KAAK,CAAC;YACpC;UACF;YACEF,GAAG,GAAG,IAAI,CAACoO,mBAAmB,CAAClO,KAAK,CAAC;YACrC;UACF;YAAqB;cAEnB,MAAM6D,aAAa,GAAG,IAAI,CAACxC,KAAK,CAACQ,QAAQ;cACzC,IAAIpC,mBAAmB,IAAI,IAAI,EAAE;gBAC/B,IAAIA,mBAAmB,CAACoE,aAAa,KAAK,IAAI,EAAE;kBAC9CpE,mBAAmB,CAACoE,aAAa,GAAGA,aAAa;gBACnD;cACF,CAAC,MAAM;gBACL,IAAI,CAAC5D,KAAK,CAACC,kBAAM,CAAC2Y,sBAAsB,EAAE;kBACxCzY,EAAE,EAAEyD;gBACN,CAAC,CAAC;cACJ;cACA/D,GAAG,GAAG,IAAI,CAAC6E,gBAAgB,CAAC,CAAC;cAC7B;YACF;UACA;YACE,IAAI,CAACzD,UAAU,CAAC,CAAC;QACrB;MACF;MACC5B,IAAI,CAASQ,GAAG,GAAGA,GAAG;MACvB,IAAIJ,IAAI,QAAmB,EAAE;QAE3BJ,IAAI,CAACM,QAAQ,GAAG,KAAK;MACvB;IACF;IAEA,OAAON,IAAI,CAACQ,GAAG;EACjB;EAIAgZ,YAAYA,CAAC/W,IAAoC,EAAEwP,OAAgB,EAAQ;IACzExP,IAAI,CAAC6N,EAAE,GAAG,IAAI;IACd7N,IAAI,CAACgX,SAAS,GAAG,KAAK;IACtBhX,IAAI,CAACyP,KAAK,GAAGD,OAAO;EACtB;EAIA+G,WAAWA,CAETvW,IAAe,EACfsV,WAAoB,EACpB9F,OAAgB,EAChByH,aAAsB,EACtBlH,gBAAyB,EACzBpS,IAAe,EACfuZ,YAAqB,GAAG,KAAK,EAC1B;IACH,IAAI,CAACH,YAAY,CAAC/W,IAAI,EAAEwP,OAAO,CAAC;IAChCxP,IAAI,CAACgX,SAAS,GAAG1B,WAAW;IAC5B,IAAI,CAACxF,KAAK,CAAC7G,KAAK,CACdkO,0BAAc,GACZC,uBAAW,IACVF,YAAY,GAAGG,uBAAW,GAAG,CAAC,CAAC,IAC/BtH,gBAAgB,GAAGuH,8BAAkB,GAAG,CAAC,CAC9C,CAAC;IACD,IAAI,CAACvW,SAAS,CAACkI,KAAK,CAAC,IAAAkG,kCAAa,EAACK,OAAO,EAAExP,IAAI,CAACgX,SAAS,CAAC,CAAC;IAC5D,IAAI,CAACO,mBAAmB,CAACvX,IAAI,EAAEiX,aAAa,CAAC;IAC7C,MAAM3S,YAAY,GAAG,IAAI,CAACkT,0BAA0B,CAACxX,IAAI,EAAErC,IAAI,EAAE,IAAI,CAAC;IACtE,IAAI,CAACoD,SAAS,CAACyI,IAAI,CAAC,CAAC;IACrB,IAAI,CAACsG,KAAK,CAACtG,IAAI,CAAC,CAAC;IAEjB,OAAOlF,YAAY;EACrB;EAKAmI,cAAcA,CAEZjC,KAAgB,EAChBiN,YAAqB,EACrBC,OAAgB,EAChBha,mBAA6C,EACN;IACvC,IAAIga,OAAO,EAAE;MACX,IAAI,CAAC9T,YAAY,CAAC,gBAAgB,CAAC;IACrC;IACA,MAAMkH,6BAA6B,GAAG,IAAI,CAACxL,KAAK,CAACuE,0BAA0B;IAC3E,IAAI,CAACvE,KAAK,CAACuE,0BAA0B,GAAG,KAAK;IAC7C,MAAM7D,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAwC,CAAC;IACpE,IAAI,CAACpE,IAAI,CAAC,CAAC;IACXhC,IAAI,CAAC2X,QAAQ,GAAG,IAAI,CAACzE,aAAa,CAChC1I,KAAK,EACY,CAACkN,OAAO,EACzBha,mBAAmB,EAEnBsC,IACF,CAAC;IACD,IAAI,CAACV,KAAK,CAACuE,0BAA0B,GAAGiH,6BAA6B;IACrE,OAAO,IAAI,CAACxK,UAAU,CACpBN,IAAI,EACJ0X,OAAO,GAAG,iBAAiB,GAAG,iBAChC,CAAC;EACH;EAKArM,oBAAoBA,CAElBrL,IAAuC,EACvCoP,MAAyC,EACzCI,OAAgB,EAChBlE,gBAAkC,EACP;IAC3B,IAAI,CAACwE,KAAK,CAAC7G,KAAK,CAACkO,0BAAc,GAAGS,uBAAW,CAAC;IAC9C,IAAIvG,KAAK,GAAG,IAAAlC,kCAAa,EAACK,OAAO,EAAE,KAAK,CAAC;IAIzC,IAAI,CAAC,IAAI,CAACtQ,KAAK,EAAU,CAAC,IAAI,IAAI,CAAC6B,SAAS,CAACqC,KAAK,EAAE;MAClDiO,KAAK,IAAIwG,6BAAQ;IACnB;IACA,IAAI,CAAC9W,SAAS,CAACkI,KAAK,CAACoI,KAAK,CAAC;IAC3B,IAAI,CAAC0F,YAAY,CAAC/W,IAAI,EAAEwP,OAAO,CAAC;IAChC,MAAM1G,yBAAyB,GAAG,IAAI,CAACxJ,KAAK,CAACyJ,sBAAsB;IAEnE,IAAIqG,MAAM,EAAE;MACV,IAAI,CAAC9P,KAAK,CAACyJ,sBAAsB,GAAG,IAAI;MACxC,IAAI,CAAC+O,0BAA0B,CAAC9X,IAAI,EAAEoP,MAAM,EAAE9D,gBAAgB,CAAC;IACjE;IACA,IAAI,CAAChM,KAAK,CAACyJ,sBAAsB,GAAG,KAAK;IACzC,IAAI,CAACgP,iBAAiB,CAAC/X,IAAI,EAAE,IAAI,CAAC;IAElC,IAAI,CAACe,SAAS,CAACyI,IAAI,CAAC,CAAC;IACrB,IAAI,CAACsG,KAAK,CAACtG,IAAI,CAAC,CAAC;IACjB,IAAI,CAAClK,KAAK,CAACyJ,sBAAsB,GAAGD,yBAAyB;IAE7D,OAAO,IAAI,CAACxI,UAAU,CAACN,IAAI,EAAE,yBAAyB,CAAC;EACzD;EAEA8X,0BAA0BA,CACxB9X,IAAuC,EACvCoP,MAAsB,EACtB9D,gBAAkC,EAC5B;IACN,IAAI,CAAC0M,gBAAgB,CAAC5I,MAAM,EAAE9D,gBAAgB,EAAE,KAAK,CAAC;IACtDtL,IAAI,CAACoP,MAAM,GAAGA,MAA+C;EAC/D;EAEAoI,0BAA0BA,CAMxBxX,IAAe,EAAErC,IAAe,EAAEsa,QAAiB,GAAG,KAAK,EAAK;IAEhE,IAAI,CAACF,iBAAiB,CAAC/X,IAAI,EAAE,KAAK,EAAEiY,QAAQ,CAAC;IAC7C,OAAO,IAAI,CAAC3X,UAAU,CAACN,IAAI,EAAErC,IAAI,CAAC;EACpC;EAGAoa,iBAAiBA,CAEf/X,IAAwB,EACxBkY,eAAgC,EAChCD,QAAiB,GAAG,KAAK,EACnB;IACN,MAAME,YAAY,GAAGD,eAAe,IAAI,CAAC,IAAI,CAAChZ,KAAK,EAAU,CAAC;IAC9D,IAAI,CAAC8J,eAAe,CAACC,KAAK,CAAC,IAAAmP,mCAAkB,EAAC,CAAC,CAAC;IAEhD,IAAID,YAAY,EAAE;MAEfnY,IAAI,CAAuCmF,IAAI,GAC9C,IAAI,CAACpF,gBAAgB,CAAC,CAAC;MACzB,IAAI,CAACsY,WAAW,CAACrY,IAAI,EAAE,KAAK,EAAEkY,eAAe,EAAE,KAAK,CAAC;IACvD,CAAC,MAAM;MACL,MAAMI,SAAS,GAAG,IAAI,CAAChZ,KAAK,CAACkH,MAAM;MAGnC,MAAMkJ,SAAS,GAAG,IAAI,CAACpQ,KAAK,CAACqQ,MAAM;MACnC,IAAI,CAACrQ,KAAK,CAACqQ,MAAM,GAAG,EAAE;MAItB,IAAI,CAAC5O,SAAS,CAACkI,KAAK,CAAC,IAAI,CAAClI,SAAS,CAACwX,YAAY,CAAC,CAAC,GAAGC,iCAAY,CAAC;MAClExY,IAAI,CAACmF,IAAI,GAAG,IAAI,CAAC0K,UAAU,CACzB,IAAI,EACJ,KAAK,EAEJ4I,sBAA+B,IAAK;QACnC,MAAMC,SAAS,GAAG,CAAC,IAAI,CAACC,iBAAiB,CAAC3Y,IAAI,CAACoP,MAAM,CAAC;QAEtD,IAAIqJ,sBAAsB,IAAIC,SAAS,EAAE;UAEvC,IAAI,CAACxa,KAAK,CAACC,kBAAM,CAACya,4BAA4B,EAAE;YAC9Cva,EAAE,EAEA,CAAC2B,IAAI,CAAC0V,IAAI,KAAK,QAAQ,IAAI1V,IAAI,CAAC0V,IAAI,KAAK,aAAa,KAEtD,CAAC,CAAC1V,IAAI,CAACjC,GAAG,GAENiC,IAAI,CAACjC,GAAG,CAACS,GAAG,CAAC0L,GAAG,GAChBlK;UACR,CAAC,CAAC;QACJ;QAEA,MAAM6Y,iBAAiB,GAAG,CAACP,SAAS,IAAI,IAAI,CAAChZ,KAAK,CAACkH,MAAM;QAIzD,IAAI,CAAC6R,WAAW,CACdrY,IAAI,EACJ,CAAC,IAAI,CAACV,KAAK,CAACkH,MAAM,IAAI,CAAC0R,eAAe,IAAI,CAACD,QAAQ,IAAI,CAACS,SAAS,EACjER,eAAe,EACfW,iBACF,CAAC;QAGD,IAAI,IAAI,CAACvZ,KAAK,CAACkH,MAAM,IAAIxG,IAAI,CAAC6N,EAAE,EAAE;UAChC,IAAI,CAACiL,eAAe,CAAC9Y,IAAI,CAAC6N,EAAE,EAAEkL,wBAAY,EAAEF,iBAAiB,CAAC;QAChE;MACF,CACF,CAAC;MACD,IAAI,CAAC9X,SAAS,CAACyI,IAAI,CAAC,CAAC;MACrB,IAAI,CAAClK,KAAK,CAACqQ,MAAM,GAAGD,SAAS;IAC/B;IACA,IAAI,CAAC1G,eAAe,CAACQ,IAAI,CAAC,CAAC;EAC7B;EAEAwP,iBAAiBA,CAAChZ,IAAuC,EAAE;IACzD,OAAOA,IAAI,CAACrC,IAAI,KAAK,YAAY;EACnC;EAEAgb,iBAAiBA,CACfvJ,MAAwD,EAC/C;IACT,KAAK,IAAI6J,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAG9J,MAAM,CAAChF,MAAM,EAAE6O,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACjD,IAAI,CAAC,IAAI,CAACD,iBAAiB,CAAC5J,MAAM,CAAC6J,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IACtD;IACA,OAAO,IAAI;EACb;EAEAZ,WAAWA,CACTrY,IAAwB,EACxBmZ,eAAwB,EAExBC,eAAgC,EAChCP,iBAA0B,GAAG,IAAI,EAC3B;IACN,MAAMQ,YAAY,GAAG,CAACF,eAAe,IAAI,IAAIG,GAAG,CAAS,CAAC;IAO1D,MAAMC,gBAAgB,GAAG;MAAE5b,IAAI,EAAE;IAAmB,CAAU;IAC9D,KAAK,MAAM6b,KAAK,IAAIxZ,IAAI,CAACoP,MAAM,EAAE;MAC/B,IAAI,CAAClN,SAAS,CAACsX,KAAK,EAAE;QACpBrX,EAAE,EAAEoX,gBAAgB;QACpBE,OAAO,EAAEC,oBAAQ;QACjBL,YAAY;QACZR;MACF,CAAC,CAAC;IACJ;EACF;EAQA3F,aAAaA,CAEX1I,KAAgB,EAChBmP,UAAoB,EACpBjc,mBAA6C,EAC7CiN,YAA4B,EACH;IACzB,MAAMC,IAA6B,GAAG,EAAE;IACxC,IAAIC,KAAK,GAAG,IAAI;IAEhB,OAAO,CAAC,IAAI,CAAC1K,GAAG,CAACqK,KAAK,CAAC,EAAE;MACvB,IAAIK,KAAK,EAAE;QACTA,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACL,IAAI,CAACpI,MAAM,GAAS,CAAC;QACrB,IAAI,IAAI,CAACvD,KAAK,CAACsL,KAAK,CAAC,EAAE;UACrB,IAAIG,YAAY,EAAE;YAChB,IAAI,CAACM,2BAA2B,CAACN,YAAY,CAAC;UAChD;UACA,IAAI,CAAC3I,IAAI,CAAC,CAAC;UACX;QACF;MACF;MAEA4I,IAAI,CAACxK,IAAI,CAAC,IAAI,CAAC8K,iBAAiB,CAACyO,UAAU,EAAEjc,mBAAmB,CAAC,CAAC;IACpE;IACA,OAAOkN,IAAI;EACb;EAcAM,iBAAiBA,CAEfyO,UAA2B,EAC3Bjc,mBAA6C,EAC7CgN,gBAAiC,EACZ;IACrB,IAAIkP,GAAG;IACP,IAAI,IAAI,CAAC1a,KAAK,GAAS,CAAC,EAAE;MACxB,IAAI,CAACya,UAAU,EAAE;QACf,IAAI,CAACzb,KAAK,CAACC,kBAAM,CAAC0b,eAAe,EAAE;UACjCxb,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACiQ,WAAW,CAAC,CAAC;UAC5BpQ,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;MACAya,GAAG,GAAG,IAAI;IACZ,CAAC,MAAM,IAAI,IAAI,CAAC1a,KAAK,GAAY,CAAC,EAAE;MAClC,MAAM0S,kBAAkB,GAAG,IAAI,CAACtS,KAAK,CAACQ,QAAQ;MAE9C8Z,GAAG,GAAG,IAAI,CAAC/H,cAAc,CACvB,IAAI,CAACuD,WAAW,CAAC1X,mBAAmB,CAAC,EACrCkU,kBACF,CAAC;IACH,CAAC,MAAM,IAAI,IAAI,CAAC1S,KAAK,GAAY,CAAC,EAAE;MAClC,IAAI,CAAC0E,YAAY,CAAC,oBAAoB,CAAC;MACvC,IAAI,CAAC8G,gBAAgB,EAAE;QACrB,IAAI,CAACxM,KAAK,CAACC,kBAAM,CAAC2b,6BAA6B,EAAE;UAC/Czb,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;QACjB,CAAC,CAAC;MACJ;MACA,MAAME,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAC,CAAC;MAC7B,IAAI,CAACpE,IAAI,CAAC,CAAC;MACX4X,GAAG,GAAG,IAAI,CAACtZ,UAAU,CAACN,IAAI,EAAE,qBAAqB,CAAC;IACpD,CAAC,MAAM;MACL4Z,GAAG,GAAG,IAAI,CAACnZ,uBAAuB,CAChC/C,mBAAmB,EACnB,IAAI,CAACmU,cACP,CAAC;IACH;IACA,OAAO+H,GAAG;EACZ;EAQAjR,eAAeA,CAACoR,OAAiB,EAAgB;IAC/C,MAAM/Z,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAe,CAAC;IAC3C,MAAMpI,IAAI,GAAG,IAAI,CAACgc,mBAAmB,CAACD,OAAO,CAAC;IAE9C,OAAO,IAAI,CAAC1J,gBAAgB,CAACrQ,IAAI,EAAEhC,IAAI,CAAC;EAC1C;EAEAqS,gBAAgBA,CACdrQ,IAAgC,EAChChC,IAAY,EACE;IACdgC,IAAI,CAAChC,IAAI,GAAGA,IAAI;IAChBgC,IAAI,CAACxB,GAAG,CAAC8E,cAAc,GAAGtF,IAAI;IAE9B,OAAO,IAAI,CAACsC,UAAU,CAACN,IAAI,EAAE,YAAY,CAAC;EAC5C;EAEAga,mBAAmBA,CAACD,OAAiB,EAAU;IAC7C,IAAI/b,IAAY;IAEhB,MAAM;MAAE8B,QAAQ;MAAEnC;IAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAErC,IAAI,IAAAuX,iCAA0B,EAAClZ,IAAI,CAAC,EAAE;MACpCK,IAAI,GAAG,IAAI,CAACsB,KAAK,CAACrB,KAAK;IACzB,CAAC,MAAM;MACL,IAAI,CAACkB,UAAU,CAAC,CAAC;IACnB;IAEA,MAAM8a,cAAc,GAAG,IAAAC,wCAAiC,EAACvc,IAAI,CAAC;IAE9D,IAAIoc,OAAO,EAAE;MAGX,IAAIE,cAAc,EAAE;QAClB,IAAI,CAACE,YAAY,IAAQ,CAAC;MAC5B;IACF,CAAC,MAAM;MACL,IAAI,CAACzD,iBAAiB,CAAC1Y,IAAI,EAAE8B,QAAQ,EAAEma,cAAc,EAAE,KAAK,CAAC;IAC/D;IAEA,IAAI,CAACjY,IAAI,CAAC,CAAC;IAEX,OAAOhE,IAAI;EACb;EAEA0Y,iBAAiBA,CACf0D,IAAY,EACZta,QAAkB,EAClBua,aAAsB,EACtBC,SAAkB,EACZ;IAEN,IAAIF,IAAI,CAAChQ,MAAM,GAAG,EAAE,EAAE;MACpB;IACF;IAGA,IAAI,CAAC,IAAAmQ,6BAAiB,EAACH,IAAI,CAAC,EAAE;MAC5B;IACF;IAEA,IAAIC,aAAa,IAAI,IAAAG,qBAAS,EAACJ,IAAI,CAAC,EAAE;MACpC,IAAI,CAAClc,KAAK,CAACC,kBAAM,CAACsc,iBAAiB,EAAE;QACnCpc,EAAE,EAAEyB,QAAQ;QACZ4a,OAAO,EAAEN;MACX,CAAC,CAAC;MACF;IACF;IAEA,MAAMO,YAAY,GAAG,CAAC,IAAI,CAACrb,KAAK,CAACkH,MAAM,GACnCoU,0BAAc,GACdN,SAAS,GACTO,oCAAwB,GACxBC,gCAAoB;IAExB,IAAIH,YAAY,CAACP,IAAI,EAAE,IAAI,CAACvJ,QAAQ,CAAC,EAAE;MACrC,IAAI,CAAC3S,KAAK,CAACC,kBAAM,CAAC4c,sBAAsB,EAAE;QACxC1c,EAAE,EAAEyB,QAAQ;QACZkb,YAAY,EAAEZ;MAChB,CAAC,CAAC;MACF;IACF,CAAC,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,IAAI,CAACrZ,SAAS,CAACC,QAAQ,EAAE;QAC3B,IAAI,CAAC9C,KAAK,CAACC,kBAAM,CAAC8c,sBAAsB,EAAE;UAAE5c,EAAE,EAAEyB;QAAS,CAAC,CAAC;QAC3D;MACF;IACF,CAAC,MAAM,IAAIsa,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,IAAI,CAACrZ,SAAS,CAACoD,QAAQ,EAAE;QAC3B,IAAI,CAACjG,KAAK,CAACC,kBAAM,CAAC+c,sBAAsB,EAAE;UAAE7c,EAAE,EAAEyB;QAAS,CAAC,CAAC;QAC3D;MACF;MAEA,IAAI,IAAI,CAACgQ,KAAK,CAACqL,aAAa,EAAE;QAC5B,IAAI,CAACjd,KAAK,CAACC,kBAAM,CAACid,mCAAmC,EAAE;UACrD/c,EAAE,EAAEyB;QACN,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACkJ,eAAe,CAACqS,+BAA+B,CAAC;QAAEhd,EAAE,EAAEyB;MAAS,CAAC,CAAC;IACxE,CAAC,MAAM,IAAIsa,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,IAAI,CAACtK,KAAK,CAACwL,+BAA+B,EAAE;QAC9C,IAAI,CAACpd,KAAK,CAACC,kBAAM,CAACod,gBAAgB,EAAE;UAAEld,EAAE,EAAEyB;QAAS,CAAC,CAAC;QACrD;MACF;IACF;EACF;EAEAmG,cAAcA,CAAA,EAAY;IACxB,IAAI,IAAI,CAAClF,SAAS,CAACoD,QAAQ,EAAE,OAAO,IAAI;IACxC,IAAI,IAAI,CAAC3E,OAAO,CAACgc,yBAAyB,IAAI,CAAC,IAAI,CAAC1L,KAAK,CAAC2L,UAAU,EAAE;MACpE,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAIAvV,UAAUA,CAAepG,QAAkB,EAAqB;IAC9D,MAAME,IAAI,GAAG,IAAI,CAACC,WAAW,CAAoBH,QAAQ,CAAC;IAE1D,IAAI,CAACkJ,eAAe,CAAC0S,+BAA+B,CAClDvd,kBAAM,CAACwd,8BAA8B,EACrC;MAEEtd,EAAE,EAAE2B;IACN,CACF,CAAC;IAED,IAAI,IAAI,CAACG,GAAG,GAAQ,CAAC,EAAE;MACrB,IAAI,CAACjC,KAAK,CAACC,kBAAM,CAACyd,iBAAiB,EAAE;QAAEvd,EAAE,EAAE2B;MAAK,CAAC,CAAC;IACpD;IAEA,IAAI,CAAC,IAAI,CAAC8P,KAAK,CAAC2L,UAAU,IAAI,CAAC,IAAI,CAACjc,OAAO,CAACgc,yBAAyB,EAAE;MACrE,IAAI,IAAI,CAACxU,gBAAgB,CAAC,CAAC,EAAE;QAC3B,IAAI,CAAC6U,2BAA2B,GAAG,IAAI;MACzC,CAAC,MAAM;QACL,IAAI,CAAC9K,iBAAiB,GAAG,IAAI;MAC/B;IACF;IAEA,IAAI,CAAC,IAAI,CAACzR,KAAK,CAACwc,SAAS,EAAE;MACzB9b,IAAI,CAAC8F,QAAQ,GAAG,IAAI,CAACjD,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC;IAClD;IAEA,OAAO,IAAI,CAACvC,UAAU,CAACN,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAEAgH,gBAAgBA,CAAA,EAAY;IAC1B,IAAI,IAAI,CAACqI,qBAAqB,CAAC,CAAC,EAAE,OAAO,IAAI;IAC7C,MAAM;MAAE1R;IAAK,CAAC,GAAG,IAAI,CAAC2B,KAAK;IAC3B,OAGE3B,IAAI,OAAe,IACnBA,IAAI,OAAc,IAClBA,IAAI,MAAgB,IACpB,IAAAsK,sBAAe,EAACtK,IAAI,CAAC,IACpBA,IAAI,QAAW,IAAI,CAAC,IAAI,CAAC2B,KAAK,CAACsO,WAAY,IAG5CjQ,IAAI,QAAc,IAClBA,IAAI,OAAa,IAGhB,IAAI,CAACsG,SAAS,CAAC,aAAa,CAAC,IAAItG,IAAI,OAAe;EAEzD;EAIAuD,UAAUA,CAAA,EAAkC;IAC1C,MAAMlB,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAoB,CAAC;IAEhD,IAAI,CAAC4C,eAAe,CAAC0S,+BAA+B,CAClDvd,kBAAM,CAAC4d,gBAAgB,EACvB;MAEE1d,EAAE,EAAE2B;IACN,CACF,CAAC;IAED,IAAI,CAACgC,IAAI,CAAC,CAAC;IACX,IAAIga,UAAU,GAAG,KAAK;IACtB,IAAIlW,QAA6B,GAAG,IAAI;IACxC,IAAI,CAAC,IAAI,CAACuJ,qBAAqB,CAAC,CAAC,EAAE;MACjC2M,UAAU,GAAG,IAAI,CAAC7b,GAAG,GAAQ,CAAC;MAC9B,QAAQ,IAAI,CAACb,KAAK,CAAC3B,IAAI;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UAIE,IAAI,CAACqe,UAAU,EAAE;QAEnB;UACElW,QAAQ,GAAG,IAAI,CAAC/F,gBAAgB,CAAC,CAAC;MACtC;IACF;IACAC,IAAI,CAACic,QAAQ,GAAGD,UAAU;IAC1Bhc,IAAI,CAAC8F,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI,CAACxF,UAAU,CAACN,IAAI,EAAE,iBAAiB,CAAC;EACjD;EAKA8D,4BAA4BA,CAAC7C,IAAkB,EAAE8B,YAAsB,EAAE;IACvE,IAAI,IAAI,CAACkB,SAAS,CAAC,CAAC,kBAAkB,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAE;MAC/D,IAAIjD,IAAI,CAACtD,IAAI,KAAK,oBAAoB,EAAE;QAGtC,IAAI,CAACO,KAAK,CAACC,kBAAM,CAAC+d,8BAA8B,EAAE;UAChD7d,EAAE,EAAE0E;QACN,CAAC,CAAC;MACJ;IACF;EACF;EAEA8B,6BAA6BA,CAACsX,SAAuB,EAAErc,QAAkB,EAAE;IACzE,IAAI,IAAI,CAACsc,iBAAiB,CAACD,SAAS,CAAC,EAAE;MACrC,MAAME,QAAQ,GAAG,IAAI,CAACpc,WAAW,CAAyBH,QAAQ,CAAC;MACnEuc,QAAQ,CAACzT,MAAM,GAAGuT,SAAS;MAC3B,OAAO,IAAI,CAAC7b,UAAU,CAAC+b,QAAQ,EAAE,sBAAsB,CAAC;IAC1D,CAAC,MAAM;MACL,MAAMA,QAAQ,GAAG,IAAI,CAACpc,WAAW,CAA4BH,QAAQ,CAAC;MACtE,IAAI,CAACwc,kCAAkC,CAACxc,QAAQ,CAAC;MACjDuc,QAAQ,CAAC/J,UAAU,GAAG6J,SAAS;MAC/B,OAAO,IAAI,CAAC7b,UAAU,CAAC+b,QAAQ,EAAE,yBAAyB,CAAC;IAC7D;EACF;EAEAD,iBAAiBA,CAAC9J,UAAwB,EAAW;IACnD,QAAQA,UAAU,CAAC3U,IAAI;MACrB,KAAK,kBAAkB;QACrB,OACE,CAAC2U,UAAU,CAACzU,QAAQ,IAAI,IAAI,CAACue,iBAAiB,CAAC9J,UAAU,CAAC9J,MAAM,CAAC;MAErE,KAAK,YAAY;QACf,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAMA8T,kCAAkCA,CAACxc,QAAkB,EAAQ;IAO3D,IAAI,IAAI,CAACZ,KAAK,GAAS,CAAC,EAAE;MACxB,MAAM,IAAI,CAAChB,KAAK,CAACC,kBAAM,CAACoe,mBAAmB,EAAE;QAAEle,EAAE,EAAE,IAAI,CAACiB,KAAK,CAACQ;MAAS,CAAC,CAAC;IAC3E;IAGA,IAAI,CAAC,IAAI,CAAC4F,qCAAqC,CAAC,CAAC,EAAE;MACjD,IAAI,CAACxH,KAAK,CAACC,kBAAM,CAACqe,mBAAmB,EAAE;QAAEne,EAAE,EAAEyB;MAAS,CAAC,CAAC;IAC1D;EACF;EAQA4E,uBAAuBA,CAAI+X,QAAiB,EAAK;IAC/C,MAAMC,sBAAsB,GAAG,IAAI,CAACpd,KAAK,CAACqd,YAAY;IACtD,IAAI,CAACrd,KAAK,CAACqd,YAAY,GAAG;MAExBC,wBAAwB,EAAE,CAAC;MAE3BC,aAAa,EAAE;IACjB,CAAC;IAED,IAAI;MACF,OAAOJ,QAAQ,CAAC,CAAC;IACnB,CAAC,SAAS;MACR,IAAI,CAACnd,KAAK,CAACqd,YAAY,GAAGD,sBAAsB;IAClD;EACF;EAUAI,kCAAkCA,CAAIL,QAAiB,EAAK;IAC1D,IAAI,IAAI,CAACxY,SAAS,CAAC,CAAC,kBAAkB,EAAE;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAE;MAE/D,MAAMwY,sBAAsB,GAAG,IAAI,CAACpd,KAAK,CAACqd,YAAY;MACtD,IAAI,CAACrd,KAAK,CAACqd,YAAY,GAAG;QAExBC,wBAAwB,EAAE,CAAC;QAE3BC,aAAa,EAAE;MACjB,CAAC;MAED,IAAI;QACF,OAAOJ,QAAQ,CAAC,CAAC;MACnB,CAAC,SAAS;QACR,IAAI,CAACnd,KAAK,CAACqd,YAAY,GAAGD,sBAAsB;MAClD;IACF,CAAC,MAAM;MAKL,OAAOD,QAAQ,CAAC,CAAC;IACnB;EACF;EAEA1X,8BAA8BA,CAAI0X,QAAiB,EAAK;IACtD,MAAMM,0BAA0B,GAAG,IAAI,CAACzd,KAAK,CAACwc,SAAS;IACvD,IAAI,CAACxc,KAAK,CAACwc,SAAS,GAAG,IAAI;IAE3B,IAAI;MACF,OAAOW,QAAQ,CAAC,CAAC;IACnB,CAAC,SAAS;MACR,IAAI,CAACnd,KAAK,CAACwc,SAAS,GAAGiB,0BAA0B;IACnD;EACF;EAEAld,UAAUA,CAAI4c,QAAiB,EAAK;IAClC,MAAMpL,KAAK,GAAG,IAAI,CAACtQ,SAAS,CAACwX,YAAY,CAAC,CAAC;IAC3C,MAAMyE,cAAc,GAAGnF,6BAAQ,GAAG,CAACxG,KAAK;IACxC,IAAI2L,cAAc,EAAE;MAClB,IAAI,CAACjc,SAAS,CAACkI,KAAK,CAACoI,KAAK,GAAGwG,6BAAQ,CAAC;MACtC,IAAI;QACF,OAAO4E,QAAQ,CAAC,CAAC;MACnB,CAAC,SAAS;QACR,IAAI,CAAC1b,SAAS,CAACyI,IAAI,CAAC,CAAC;MACvB;IACF;IACA,OAAOiT,QAAQ,CAAC,CAAC;EACnB;EAEA9c,aAAaA,CAAI8c,QAAiB,EAAK;IACrC,MAAMpL,KAAK,GAAG,IAAI,CAACtQ,SAAS,CAACwX,YAAY,CAAC,CAAC;IAC3C,MAAM0E,gBAAgB,GAAGpF,6BAAQ,GAAGxG,KAAK;IACzC,IAAI4L,gBAAgB,EAAE;MACpB,IAAI,CAAClc,SAAS,CAACkI,KAAK,CAACoI,KAAK,GAAG,CAACwG,6BAAQ,CAAC;MACvC,IAAI;QACF,OAAO4E,QAAQ,CAAC,CAAC;MACnB,CAAC,SAAS;QACR,IAAI,CAAC1b,SAAS,CAACyI,IAAI,CAAC,CAAC;MACvB;IACF;IACA,OAAOiT,QAAQ,CAAC,CAAC;EACnB;EAIA5N,sBAAsBA,CAAA,EAAS;IAC7B,IAAI,CAACvP,KAAK,CAACqd,YAAY,CAACE,aAAa,GAAG,CAAC;EAC3C;EAEAnO,uCAAuCA,CAAA,EAAY;IACjD,OAAO,IAAI,CAACpP,KAAK,CAACqd,YAAY,CAACC,wBAAwB,IAAI,CAAC;EAC9D;EAEAlX,qCAAqCA,CAAA,EAAY;IAC/C,OACE,IAAI,CAACpG,KAAK,CAACqd,YAAY,CAACE,aAAa,IAAI,IAAI,IAC7C,IAAI,CAACvd,KAAK,CAACqd,YAAY,CAACE,aAAa,IAAI,CAAC;EAE9C;EAEA7X,uBAAuBA,CAAerB,IAAY,EAAgB;IAChE,MAAM7D,QAAQ,GAAG,IAAI,CAACR,KAAK,CAACQ,QAAQ;IAEpC,IAAI,CAACR,KAAK,CAACT,gBAAgB,GAAG,IAAI,CAACS,KAAK,CAACb,KAAK;IAC9C,MAAMqM,6BAA6B,GAAG,IAAI,CAACxL,KAAK,CAACuE,0BAA0B;IAC3E,IAAI,CAACvE,KAAK,CAACuE,0BAA0B,GAAG,IAAI;IAE5C,MAAMqZ,GAAG,GAAG,IAAI,CAACpa,WAAW,CAC1B,IAAI,CAACH,wBAAwB,CAAC,CAAC,EAC/B7C,QAAQ,EACR6D,IACF,CAAC;IAED,IAAI,CAACrE,KAAK,CAACuE,0BAA0B,GAAGiH,6BAA6B;IAErE,OAAOoS,GAAG;EACZ;EAGAvP,qBAAqBA,CAAA,EAAmC;IACtD,IAAI,CAAC/J,YAAY,CAAC,cAAc,CAAC;IACjC,MAAM5D,IAAI,GAAG,IAAI,CAACoG,SAAS,CAAqB,CAAC;IACjD,IAAI,CAACpE,IAAI,CAAC,CAAC;IACX,IAAI,CAAC,IAAI,CAAC9C,KAAK,EAAU,CAAC,EAAE;MAC1B,IAAI,CAACC,UAAU,CAAC,IAAI,GAAW,CAAC;IAClC;IAEA,MAAMge,OAAO,GAAG,IAAI,CAACld,WAAW,CAAY,IAAI,CAACX,KAAK,CAAC8O,MAAM,CAAC;IAC9D,IAAI,CAACpM,IAAI,CAAC,CAAC;IAEX,MAAMob,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAiB,IAAI,CAAC;IAChE,IAAI,CAACte,kBAAkB,CAAC,CAAC;IAEzB,IAAI;MACFiB,IAAI,CAACmF,IAAI,GAAG,IAAI,CAACmY,YAAY,CAACH,OAAO,KAAa,QAAQ,CAAC;IAC7D,CAAC,SAAS;MACRC,YAAY,CAAC,CAAC;IAChB;IACA,OAAO,IAAI,CAAC9c,UAAU,CAAqBN,IAAI,EAAE,kBAAkB,CAAC;EACtE;EAGAuV,+BAA+BA,CAE7BhY,IAAmD,EAC7C,CAAC;AACX;AAACggB,OAAA,CAAAC,OAAA,GAAApgB,gBAAA"}