{"version": 3, "names": ["_scopeflags", "require", "_parseError", "ClassScope", "constructor", "privateNames", "Set", "loneAccessors", "Map", "undefinedPrivateNames", "exports", "ClassScopeHandler", "parser", "stack", "current", "length", "enter", "push", "exit", "oldClassScope", "pop", "name", "loc", "Array", "from", "has", "set", "raise", "Errors", "InvalidPrivateFieldResolution", "at", "identifierName", "declarePrivateName", "elementType", "redefined", "CLASS_ELEMENT_KIND_ACCESSOR", "accessor", "get", "oldStatic", "CLASS_ELEMENT_FLAG_STATIC", "newStatic", "oldKind", "newKind", "delete", "PrivateNameRedeclaration", "add", "usePrivateName", "classScope", "default"], "sources": ["../../src/util/class-scope.ts"], "sourcesContent": ["import {\n  CLASS_ELEMENT_KIND_ACCESSOR,\n  CLASS_ELEMENT_FLAG_STATIC,\n  type ClassElementTypes,\n} from \"./scopeflags\";\nimport type { Position } from \"./location\";\nimport { Errors } from \"../parse-error\";\nimport type Tokenizer from \"../tokenizer\";\n\nexport class ClassScope {\n  // A list of private named declared in the current class\n  privateNames: Set<string> = new Set();\n\n  // A list of private getters of setters without their counterpart\n  loneAccessors: Map<string, ClassElementTypes> = new Map();\n\n  // A list of private names used before being defined, mapping to\n  // their position.\n  undefinedPrivateNames: Map<string, Position> = new Map();\n}\n\nexport default class ClassScopeHandler {\n  parser: Tokenizer;\n  stack: Array<ClassScope> = [];\n  undefinedPrivateNames: Map<string, Position> = new Map();\n\n  constructor(parser: Tokenizer) {\n    this.parser = parser;\n  }\n\n  current(): ClassScope {\n    return this.stack[this.stack.length - 1];\n  }\n\n  enter() {\n    this.stack.push(new ClassScope());\n  }\n\n  exit() {\n    const oldClassScope = this.stack.pop();\n\n    // Migrate the usage of not yet defined private names to the outer\n    // class scope, or raise an error if we reached the top-level scope.\n\n    const current = this.current();\n\n    // Array.from is needed because this is compiled to an array-like for loop\n    for (const [name, loc] of Array.from(oldClassScope.undefinedPrivateNames)) {\n      if (current) {\n        if (!current.undefinedPrivateNames.has(name)) {\n          current.undefinedPrivateNames.set(name, loc);\n        }\n      } else {\n        this.parser.raise(Errors.InvalidPrivateFieldResolution, {\n          at: loc,\n          identifierName: name,\n        });\n      }\n    }\n  }\n\n  declarePrivateName(\n    name: string,\n    elementType: ClassElementTypes,\n    loc: Position,\n  ) {\n    const { privateNames, loneAccessors, undefinedPrivateNames } =\n      this.current();\n    let redefined = privateNames.has(name);\n\n    if (elementType & CLASS_ELEMENT_KIND_ACCESSOR) {\n      const accessor = redefined && loneAccessors.get(name);\n      if (accessor) {\n        const oldStatic = accessor & CLASS_ELEMENT_FLAG_STATIC;\n        const newStatic = elementType & CLASS_ELEMENT_FLAG_STATIC;\n\n        const oldKind = accessor & CLASS_ELEMENT_KIND_ACCESSOR;\n        const newKind = elementType & CLASS_ELEMENT_KIND_ACCESSOR;\n\n        // The private name can be duplicated only if it is used by\n        // two accessors with different kind (get and set), and if\n        // they have the same placement (static or not).\n        redefined = oldKind === newKind || oldStatic !== newStatic;\n\n        if (!redefined) loneAccessors.delete(name);\n      } else if (!redefined) {\n        loneAccessors.set(name, elementType);\n      }\n    }\n\n    if (redefined) {\n      this.parser.raise(Errors.PrivateNameRedeclaration, {\n        at: loc,\n        identifierName: name,\n      });\n    }\n\n    privateNames.add(name);\n    undefinedPrivateNames.delete(name);\n  }\n\n  usePrivateName(name: string, loc: Position) {\n    let classScope;\n    for (classScope of this.stack) {\n      if (classScope.privateNames.has(name)) return;\n    }\n\n    if (classScope) {\n      classScope.undefinedPrivateNames.set(name, loc);\n    } else {\n      // top-level\n      this.parser.raise(Errors.InvalidPrivateFieldResolution, {\n        at: loc,\n        identifierName: name,\n      });\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAMA,IAAAC,WAAA,GAAAD,OAAA;AAGO,MAAME,UAAU,CAAC;EAAAC,YAAA;IAAA,KAEtBC,YAAY,GAAgB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAGrCC,aAAa,GAAmC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAIzDC,qBAAqB,GAA0B,IAAID,GAAG,CAAC,CAAC;EAAA;AAC1D;AAACE,OAAA,CAAAP,UAAA,GAAAA,UAAA;AAEc,MAAMQ,iBAAiB,CAAC;EAKrCP,WAAWA,CAACQ,MAAiB,EAAE;IAAA,KAJ/BA,MAAM;IAAA,KACNC,KAAK,GAAsB,EAAE;IAAA,KAC7BJ,qBAAqB,GAA0B,IAAID,GAAG,CAAC,CAAC;IAGtD,IAAI,CAACI,MAAM,GAAGA,MAAM;EACtB;EAEAE,OAAOA,CAAA,EAAe;IACpB,OAAO,IAAI,CAACD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;EAC1C;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACH,KAAK,CAACI,IAAI,CAAC,IAAId,UAAU,CAAC,CAAC,CAAC;EACnC;EAEAe,IAAIA,CAAA,EAAG;IACL,MAAMC,aAAa,GAAG,IAAI,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC;IAKtC,MAAMN,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IAG9B,KAAK,MAAM,CAACO,IAAI,EAAEC,GAAG,CAAC,IAAIC,KAAK,CAACC,IAAI,CAACL,aAAa,CAACV,qBAAqB,CAAC,EAAE;MACzE,IAAIK,OAAO,EAAE;QACX,IAAI,CAACA,OAAO,CAACL,qBAAqB,CAACgB,GAAG,CAACJ,IAAI,CAAC,EAAE;UAC5CP,OAAO,CAACL,qBAAqB,CAACiB,GAAG,CAACL,IAAI,EAAEC,GAAG,CAAC;QAC9C;MACF,CAAC,MAAM;QACL,IAAI,CAACV,MAAM,CAACe,KAAK,CAACC,kBAAM,CAACC,6BAA6B,EAAE;UACtDC,EAAE,EAAER,GAAG;UACPS,cAAc,EAAEV;QAClB,CAAC,CAAC;MACJ;IACF;EACF;EAEAW,kBAAkBA,CAChBX,IAAY,EACZY,WAA8B,EAC9BX,GAAa,EACb;IACA,MAAM;MAAEjB,YAAY;MAAEE,aAAa;MAAEE;IAAsB,CAAC,GAC1D,IAAI,CAACK,OAAO,CAAC,CAAC;IAChB,IAAIoB,SAAS,GAAG7B,YAAY,CAACoB,GAAG,CAACJ,IAAI,CAAC;IAEtC,IAAIY,WAAW,GAAGE,uCAA2B,EAAE;MAC7C,MAAMC,QAAQ,GAAGF,SAAS,IAAI3B,aAAa,CAAC8B,GAAG,CAAChB,IAAI,CAAC;MACrD,IAAIe,QAAQ,EAAE;QACZ,MAAME,SAAS,GAAGF,QAAQ,GAAGG,qCAAyB;QACtD,MAAMC,SAAS,GAAGP,WAAW,GAAGM,qCAAyB;QAEzD,MAAME,OAAO,GAAGL,QAAQ,GAAGD,uCAA2B;QACtD,MAAMO,OAAO,GAAGT,WAAW,GAAGE,uCAA2B;QAKzDD,SAAS,GAAGO,OAAO,KAAKC,OAAO,IAAIJ,SAAS,KAAKE,SAAS;QAE1D,IAAI,CAACN,SAAS,EAAE3B,aAAa,CAACoC,MAAM,CAACtB,IAAI,CAAC;MAC5C,CAAC,MAAM,IAAI,CAACa,SAAS,EAAE;QACrB3B,aAAa,CAACmB,GAAG,CAACL,IAAI,EAAEY,WAAW,CAAC;MACtC;IACF;IAEA,IAAIC,SAAS,EAAE;MACb,IAAI,CAACtB,MAAM,CAACe,KAAK,CAACC,kBAAM,CAACgB,wBAAwB,EAAE;QACjDd,EAAE,EAAER,GAAG;QACPS,cAAc,EAAEV;MAClB,CAAC,CAAC;IACJ;IAEAhB,YAAY,CAACwC,GAAG,CAACxB,IAAI,CAAC;IACtBZ,qBAAqB,CAACkC,MAAM,CAACtB,IAAI,CAAC;EACpC;EAEAyB,cAAcA,CAACzB,IAAY,EAAEC,GAAa,EAAE;IAC1C,IAAIyB,UAAU;IACd,KAAKA,UAAU,IAAI,IAAI,CAAClC,KAAK,EAAE;MAC7B,IAAIkC,UAAU,CAAC1C,YAAY,CAACoB,GAAG,CAACJ,IAAI,CAAC,EAAE;IACzC;IAEA,IAAI0B,UAAU,EAAE;MACdA,UAAU,CAACtC,qBAAqB,CAACiB,GAAG,CAACL,IAAI,EAAEC,GAAG,CAAC;IACjD,CAAC,MAAM;MAEL,IAAI,CAACV,MAAM,CAACe,KAAK,CAACC,kBAAM,CAACC,6BAA6B,EAAE;QACtDC,EAAE,EAAER,GAAG;QACPS,cAAc,EAAEV;MAClB,CAAC,CAAC;IACJ;EACF;AACF;AAACX,OAAA,CAAAsC,OAAA,GAAArC,iBAAA"}