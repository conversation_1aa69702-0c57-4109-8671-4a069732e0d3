{"version": 3, "names": ["_t", "require", "assignmentExpression", "expressionStatement", "identifier", "visitor", "<PERSON><PERSON>", "path", "state", "kind", "skip", "FunctionParent", "VariableDeclaration", "node", "nodes", "declarations", "get", "firstId", "declar", "id", "init", "push", "name", "Object", "keys", "getBindingIdentifiers", "emit", "parentPath", "isFor", "left", "replaceWith", "replaceWithMultiple", "hoistVariables", "traverse"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  assignmentExpression,\n  expressionStatement,\n  identifier,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath, Visitor } from \"@babel/traverse\";\n\nexport type EmitFunction = (\n  id: t.Identifier,\n  idName: string,\n  hasInit: boolean,\n) => any;\n\ntype State = {\n  kind: \"var\" | \"let\";\n  emit: EmitFunction;\n};\n\ntype Unpacked<T> = T extends (infer U)[] ? U : T;\n\nconst visitor: Visitor<State> = {\n  Scope(path, state) {\n    if (state.kind === \"let\") path.skip();\n  },\n\n  FunctionParent(path) {\n    path.skip();\n  },\n\n  VariableDeclaration(path, state) {\n    if (state.kind && path.node.kind !== state.kind) return;\n\n    const nodes = [];\n\n    const declarations: ReadonlyArray<\n      NodePath<Unpacked<t.VariableDeclaration[\"declarations\"]>>\n    > = path.get(\"declarations\");\n    let firstId;\n\n    for (const declar of declarations) {\n      firstId = declar.node.id;\n\n      if (declar.node.init) {\n        nodes.push(\n          expressionStatement(\n            assignmentExpression(\"=\", declar.node.id, declar.node.init),\n          ),\n        );\n      }\n\n      for (const name of Object.keys(declar.getBindingIdentifiers())) {\n        state.emit(identifier(name), name, declar.node.init !== null);\n      }\n    }\n\n    // for (var i in test)\n    if (path.parentPath.isFor({ left: path.node })) {\n      path.replaceWith(firstId);\n    } else {\n      path.replaceWithMultiple(nodes);\n    }\n  },\n};\n\nexport default function hoistVariables(\n  path: NodePath,\n  emit: EmitFunction,\n  kind: \"var\" | \"let\" = \"var\",\n) {\n  path.traverse(visitor, { kind, emit });\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAIsB;EAHpBC,oBAAoB;EACpBC,mBAAmB;EACnBC;AAAU,IAAAJ,EAAA;AAkBZ,MAAMK,OAAuB,GAAG;EAC9BC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACC,IAAI,KAAK,KAAK,EAAEF,IAAI,CAACG,IAAI,CAAC,CAAC;EACvC,CAAC;EAEDC,cAAcA,CAACJ,IAAI,EAAE;IACnBA,IAAI,CAACG,IAAI,CAAC,CAAC;EACb,CAAC;EAEDE,mBAAmBA,CAACL,IAAI,EAAEC,KAAK,EAAE;IAC/B,IAAIA,KAAK,CAACC,IAAI,IAAIF,IAAI,CAACM,IAAI,CAACJ,IAAI,KAAKD,KAAK,CAACC,IAAI,EAAE;IAEjD,MAAMK,KAAK,GAAG,EAAE;IAEhB,MAAMC,YAEL,GAAGR,IAAI,CAACS,GAAG,CAAC,cAAc,CAAC;IAC5B,IAAIC,OAAO;IAEX,KAAK,MAAMC,MAAM,IAAIH,YAAY,EAAE;MACjCE,OAAO,GAAGC,MAAM,CAACL,IAAI,CAACM,EAAE;MAExB,IAAID,MAAM,CAACL,IAAI,CAACO,IAAI,EAAE;QACpBN,KAAK,CAACO,IAAI,CACRlB,mBAAmB,CACjBD,oBAAoB,CAAC,GAAG,EAAEgB,MAAM,CAACL,IAAI,CAACM,EAAE,EAAED,MAAM,CAACL,IAAI,CAACO,IAAI,CAC5D,CACF,CAAC;MACH;MAEA,KAAK,MAAME,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACN,MAAM,CAACO,qBAAqB,CAAC,CAAC,CAAC,EAAE;QAC9DjB,KAAK,CAACkB,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAAC,EAAEA,IAAI,EAAEJ,MAAM,CAACL,IAAI,CAACO,IAAI,KAAK,IAAI,CAAC;MAC/D;IACF;IAGA,IAAIb,IAAI,CAACoB,UAAU,CAACC,KAAK,CAAC;MAAEC,IAAI,EAAEtB,IAAI,CAACM;IAAK,CAAC,CAAC,EAAE;MAC9CN,IAAI,CAACuB,WAAW,CAACb,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLV,IAAI,CAACwB,mBAAmB,CAACjB,KAAK,CAAC;IACjC;EACF;AACF,CAAC;AAEc,SAASkB,cAAcA,CACpCzB,IAAc,EACdmB,IAAkB,EAClBjB,IAAmB,GAAG,KAAK,EAC3B;EACAF,IAAI,CAAC0B,QAAQ,CAAC5B,OAAO,EAAE;IAAEI,IAAI;IAAEiB;EAAK,CAAC,CAAC;AACxC"}