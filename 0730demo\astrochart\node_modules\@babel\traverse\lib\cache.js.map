{"version": 3, "names": ["path", "WeakMap", "exports", "scope", "clear", "clear<PERSON>ath", "clearScope"], "sources": ["../src/cache.ts"], "sourcesContent": ["import type { Node } from \"@babel/types\";\nimport type NodePath from \"./path\";\nimport type Scope from \"./scope\";\n\nexport let path: WeakMap<Node, Map<Node, NodePath>> = new WeakMap();\nexport let scope: WeakMap<Node, Scope> = new WeakMap();\n\nexport function clear() {\n  clearPath();\n  clearScope();\n}\n\nexport function clearPath() {\n  path = new WeakMap();\n}\n\nexport function clearScope() {\n  scope = new WeakMap();\n}\n"], "mappings": ";;;;;;;;;AAIO,IAAIA,IAAwC,GAAG,IAAIC,OAAO,CAAC,CAAC;AAACC,OAAA,CAAAF,IAAA,GAAAA,IAAA;AAC7D,IAAIG,KAA2B,GAAG,IAAIF,OAAO,CAAC,CAAC;AAACC,OAAA,CAAAC,KAAA,GAAAA,KAAA;AAEhD,SAASC,KAAKA,CAAA,EAAG;EACtBC,SAAS,CAAC,CAAC;EACXC,UAAU,CAAC,CAAC;AACd;AAEO,SAASD,SAASA,CAAA,EAAG;EAC1BH,OAAA,CAAAF,IAAA,GAAAA,IAAI,GAAG,IAAIC,OAAO,CAAC,CAAC;AACtB;AAEO,SAASK,UAAUA,CAAA,EAAG;EAC3BJ,OAAA,CAAAC,KAAA,GAAAA,KAAK,GAAG,IAAIF,OAAO,CAAC,CAAC;AACvB"}