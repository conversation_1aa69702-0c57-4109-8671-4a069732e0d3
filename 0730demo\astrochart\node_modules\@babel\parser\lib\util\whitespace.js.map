{"version": 3, "names": ["lineBreak", "exports", "lineBreakG", "RegExp", "source", "isNewLine", "code", "skipWhiteSpace", "skipWhiteSpaceInLine", "skipWhiteSpaceToLineBreak", "isWhitespace"], "sources": ["../../src/util/whitespace.ts"], "sourcesContent": ["import * as charCodes from \"charcodes\";\n\n// Matches a whole line break (where CR<PERSON> is considered a single\n// line break). Used to count lines.\nexport const lineBreak = /\\r\\n?|[\\n\\u2028\\u2029]/;\nexport const lineBreakG = new RegExp(lineBreak.source, \"g\");\n\n// https://tc39.github.io/ecma262/#sec-line-terminators\nexport function isNewLine(code: number): boolean {\n  switch (code) {\n    case charCodes.lineFeed:\n    case charCodes.carriageReturn:\n    case charCodes.lineSeparator:\n    case charCodes.paragraphSeparator:\n      return true;\n\n    default:\n      return false;\n  }\n}\n\nexport const skipWhiteSpace = /(?:\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/g;\n\nexport const skipWhiteSpaceInLine =\n  /(?:[^\\S\\n\\r\\u2028\\u2029]|\\/\\/.*|\\/\\*.*?\\*\\/)*/g;\n\n// Skip whitespace and single-line comments, including /* no newline here */.\n// After this RegExp matches, its lastIndex points to a line terminator, or\n// the start of multi-line comment (which is effectively a line terminator),\n// or the end of string.\nexport const skipWhiteSpaceToLineBreak = new RegExp(\n  // Unfortunately JS doesn't support Perl's atomic /(?>pattern)/ or\n  // possessive quantifiers, so we use a trick to prevent backtracking\n  // when the look-ahead for line terminator fails.\n  \"(?=(\" +\n    // Capture the whitespace and comments that should be skipped inside\n    // a look-ahead assertion, and then re-match the group as a unit.\n    skipWhiteSpaceInLine.source +\n    \"))\\\\1\" +\n    // Look-ahead for either line terminator, start of multi-line comment,\n    // or end of string.\n    /(?=[\\n\\r\\u2028\\u2029]|\\/\\*(?!.*?\\*\\/)|$)/.source,\n  \"y\", // sticky\n);\n\n// https://tc39.github.io/ecma262/#sec-white-space\nexport function isWhitespace(code: number): boolean {\n  switch (code) {\n    case 0x0009: // CHARACTER TABULATION\n    case 0x000b: // LINE TABULATION\n    case 0x000c: // FORM FEED\n    case charCodes.space:\n    case charCodes.nonBreakingSpace:\n    case charCodes.oghamSpaceMark:\n    case 0x2000: // EN QUAD\n    case 0x2001: // EM QUAD\n    case 0x2002: // EN SPACE\n    case 0x2003: // EM SPACE\n    case 0x2004: // THREE-PER-EM SPACE\n    case 0x2005: // FOUR-PER-EM SPACE\n    case 0x2006: // SIX-PER-EM SPACE\n    case 0x2007: // FIGURE SPACE\n    case 0x2008: // PUNCTUATION SPACE\n    case 0x2009: // THIN SPACE\n    case 0x200a: // HAIR SPACE\n    case 0x202f: // NARROW NO-BREAK SPACE\n    case 0x205f: // MEDIUM MATHEMATICAL SPACE\n    case 0x3000: // IDEOGRAPHIC SPACE\n    case 0xfeff: // ZERO WIDTH NO-BREAK SPACE\n      return true;\n\n    default:\n      return false;\n  }\n}\n"], "mappings": ";;;;;;;;AAIO,MAAMA,SAAS,GAAG,wBAAwB;AAACC,OAAA,CAAAD,SAAA,GAAAA,SAAA;AAC3C,MAAME,UAAU,GAAG,IAAIC,MAAM,CAACH,SAAS,CAACI,MAAM,EAAE,GAAG,CAAC;AAACH,OAAA,CAAAC,UAAA,GAAAA,UAAA;AAGrD,SAASG,SAASA,CAACC,IAAY,EAAW;EAC/C,QAAQA,IAAI;IACV;IACA;IACA;IACA;MACE,OAAO,IAAI;IAEb;MACE,OAAO,KAAK;EAChB;AACF;AAEO,MAAMC,cAAc,GAAG,+BAA+B;AAACN,OAAA,CAAAM,cAAA,GAAAA,cAAA;AAEvD,MAAMC,oBAAoB,GAC/B,gDAAgD;AAACP,OAAA,CAAAO,oBAAA,GAAAA,oBAAA;AAM5C,MAAMC,yBAAyB,GAAG,IAAIN,MAAM,CAIjD,MAAM,GAGJK,oBAAoB,CAACJ,MAAM,GAC3B,OAAO,GAGP,0CAA0C,CAACA,MAAM,EACnD,GACF,CAAC;AAACH,OAAA,CAAAQ,yBAAA,GAAAA,yBAAA;AAGK,SAASC,YAAYA,CAACJ,IAAY,EAAW;EAClD,QAAQA,IAAI;IACV,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX;IACA;IACA;IACA,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO,IAAI;IAEb;MACE,OAAO,KAAK;EAChB;AACF"}