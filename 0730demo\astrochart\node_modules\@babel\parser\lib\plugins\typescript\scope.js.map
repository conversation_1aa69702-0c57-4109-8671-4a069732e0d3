{"version": 3, "names": ["_scope", "require", "_scopeflags", "_parseError", "TypeScriptScope", "<PERSON><PERSON>", "constructor", "args", "types", "Set", "enums", "constEnums", "classes", "exportOnlyBindings", "TypeScriptScopeHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "importsStack", "createScope", "flags", "push", "enter", "SCOPE_TS_MODULE", "exit", "pop", "hasImport", "name", "allowShadow", "len", "length", "has", "i", "declareName", "bindingType", "loc", "BIND_FLAGS_TS_IMPORT", "parser", "raise", "Errors", "VarRedeclaration", "at", "identifierName", "add", "scope", "currentScope", "BIND_FLAGS_TS_EXPORT_ONLY", "maybeExportDefined", "BIND_KIND_TYPE", "BIND_KIND_VALUE", "checkRedeclarationInScope", "BIND_FLAGS_TS_ENUM", "BIND_FLAGS_TS_CONST_ENUM", "BIND_FLAGS_CLASS", "isRedeclaredInScope", "isConst", "wasConst", "lexical", "checkLocalExport", "id", "scopeStack", "exports", "default"], "sources": ["../../../src/plugins/typescript/scope.ts"], "sourcesContent": ["import type { Position } from \"../../util/location\";\nimport Sc<PERSON><PERSON><PERSON><PERSON>, { <PERSON><PERSON> } from \"../../util/scope\";\nimport {\n  BIND_KIND_TYPE,\n  BIND_FLAGS_TS_ENUM,\n  BIND_FLAGS_TS_CONST_ENUM,\n  BIND_FLAGS_TS_EXPORT_ONLY,\n  BIND_KIND_VALUE,\n  BIND_FLAGS_CLASS,\n  type ScopeFlags,\n  type BindingTypes,\n  BIND_FLAGS_TS_IMPORT,\n  SCOPE_TS_MODULE,\n} from \"../../util/scopeflags\";\nimport type * as N from \"../../types\";\nimport { Errors } from \"../../parse-error\";\n\nclass TypeScriptScope extends Scope {\n  types: Set<string> = new Set();\n\n  // enums (which are also in .types)\n  enums: Set<string> = new Set();\n\n  // const enums (which are also in .enums and .types)\n  constEnums: Set<string> = new Set();\n\n  // classes (which are also in .lexical) and interface (which are also in .types)\n  classes: Set<string> = new Set();\n\n  // namespaces and ambient functions (or classes) are too difficult to track,\n  // especially without type analysis.\n  // We need to track them anyway, to avoid \"X is not defined\" errors\n  // when exporting them.\n  exportOnlyBindings: Set<string> = new Set();\n}\n\n// See https://github.com/babel/babel/pull/9766#discussion_r268920730 for an\n// explanation of how typescript handles scope.\n\nexport default class TypeScriptScopeHandler extends ScopeHandler<TypeScriptScope> {\n  importsStack: Set<string>[] = [];\n\n  createScope(flags: ScopeFlags): TypeScriptScope {\n    this.importsStack.push(new Set()); // Always keep the top-level scope for export checks.\n\n    return new TypeScriptScope(flags);\n  }\n\n  enter(flags: number): void {\n    if (flags == SCOPE_TS_MODULE) {\n      this.importsStack.push(new Set());\n    }\n\n    super.enter(flags);\n  }\n\n  exit() {\n    const flags = super.exit();\n\n    if (flags == SCOPE_TS_MODULE) {\n      this.importsStack.pop();\n    }\n\n    return flags;\n  }\n\n  hasImport(name: string, allowShadow?: boolean) {\n    const len = this.importsStack.length;\n    if (this.importsStack[len - 1].has(name)) {\n      return true;\n    }\n    if (!allowShadow && len > 1) {\n      for (let i = 0; i < len - 1; i++) {\n        if (this.importsStack[i].has(name)) return true;\n      }\n    }\n    return false;\n  }\n\n  declareName(name: string, bindingType: BindingTypes, loc: Position) {\n    if (bindingType & BIND_FLAGS_TS_IMPORT) {\n      if (this.hasImport(name, true)) {\n        this.parser.raise(Errors.VarRedeclaration, {\n          at: loc,\n          identifierName: name,\n        });\n      }\n      this.importsStack[this.importsStack.length - 1].add(name);\n      return;\n    }\n\n    const scope = this.currentScope();\n    if (bindingType & BIND_FLAGS_TS_EXPORT_ONLY) {\n      this.maybeExportDefined(scope, name);\n      scope.exportOnlyBindings.add(name);\n      return;\n    }\n\n    super.declareName(name, bindingType, loc);\n\n    if (bindingType & BIND_KIND_TYPE) {\n      if (!(bindingType & BIND_KIND_VALUE)) {\n        // \"Value\" bindings have already been registered by the superclass.\n        this.checkRedeclarationInScope(scope, name, bindingType, loc);\n        this.maybeExportDefined(scope, name);\n      }\n      scope.types.add(name);\n    }\n    if (bindingType & BIND_FLAGS_TS_ENUM) scope.enums.add(name);\n    if (bindingType & BIND_FLAGS_TS_CONST_ENUM) scope.constEnums.add(name);\n    if (bindingType & BIND_FLAGS_CLASS) scope.classes.add(name);\n  }\n\n  isRedeclaredInScope(\n    scope: TypeScriptScope,\n    name: string,\n    bindingType: BindingTypes,\n  ): boolean {\n    if (scope.enums.has(name)) {\n      if (bindingType & BIND_FLAGS_TS_ENUM) {\n        // Enums can be merged with other enums if they are both\n        //  const or both non-const.\n        const isConst = !!(bindingType & BIND_FLAGS_TS_CONST_ENUM);\n        const wasConst = scope.constEnums.has(name);\n        return isConst !== wasConst;\n      }\n      return true;\n    }\n    if (bindingType & BIND_FLAGS_CLASS && scope.classes.has(name)) {\n      if (scope.lexical.has(name)) {\n        // Classes can be merged with interfaces\n        return !!(bindingType & BIND_KIND_VALUE);\n      } else {\n        // Interface can be merged with other classes or interfaces\n        return false;\n      }\n    }\n    if (bindingType & BIND_KIND_TYPE && scope.types.has(name)) {\n      return true;\n    }\n\n    return super.isRedeclaredInScope(scope, name, bindingType);\n  }\n\n  checkLocalExport(id: N.Identifier) {\n    const { name } = id;\n\n    if (this.hasImport(name)) return;\n\n    const len = this.scopeStack.length;\n    for (let i = len - 1; i >= 0; i--) {\n      const scope = this.scopeStack[i];\n      if (scope.types.has(name) || scope.exportOnlyBindings.has(name)) return;\n    }\n\n    super.checkLocalExport(id);\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAaA,IAAAE,WAAA,GAAAF,OAAA;AAEA,MAAMG,eAAe,SAASC,YAAK,CAAC;EAAAC,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAClCC,KAAK,GAAgB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAG9BC,KAAK,GAAgB,IAAID,GAAG,CAAC,CAAC;IAAA,KAG9BE,UAAU,GAAgB,IAAIF,GAAG,CAAC,CAAC;IAAA,KAGnCG,OAAO,GAAgB,IAAIH,GAAG,CAAC,CAAC;IAAA,KAMhCI,kBAAkB,GAAgB,IAAIJ,GAAG,CAAC,CAAC;EAAA;AAC7C;AAKe,MAAMK,sBAAsB,SAASC,cAAY,CAAkB;EAAAT,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAChFS,YAAY,GAAkB,EAAE;EAAA;EAEhCC,WAAWA,CAACC,KAAiB,EAAmB;IAC9C,IAAI,CAACF,YAAY,CAACG,IAAI,CAAC,IAAIV,GAAG,CAAC,CAAC,CAAC;IAEjC,OAAO,IAAIL,eAAe,CAACc,KAAK,CAAC;EACnC;EAEAE,KAAKA,CAACF,KAAa,EAAQ;IACzB,IAAIA,KAAK,IAAIG,2BAAe,EAAE;MAC5B,IAAI,CAACL,YAAY,CAACG,IAAI,CAAC,IAAIV,GAAG,CAAC,CAAC,CAAC;IACnC;IAEA,KAAK,CAACW,KAAK,CAACF,KAAK,CAAC;EACpB;EAEAI,IAAIA,CAAA,EAAG;IACL,MAAMJ,KAAK,GAAG,KAAK,CAACI,IAAI,CAAC,CAAC;IAE1B,IAAIJ,KAAK,IAAIG,2BAAe,EAAE;MAC5B,IAAI,CAACL,YAAY,CAACO,GAAG,CAAC,CAAC;IACzB;IAEA,OAAOL,KAAK;EACd;EAEAM,SAASA,CAACC,IAAY,EAAEC,WAAqB,EAAE;IAC7C,MAAMC,GAAG,GAAG,IAAI,CAACX,YAAY,CAACY,MAAM;IACpC,IAAI,IAAI,CAACZ,YAAY,CAACW,GAAG,GAAG,CAAC,CAAC,CAACE,GAAG,CAACJ,IAAI,CAAC,EAAE;MACxC,OAAO,IAAI;IACb;IACA,IAAI,CAACC,WAAW,IAAIC,GAAG,GAAG,CAAC,EAAE;MAC3B,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,EAAE,EAAE;QAChC,IAAI,IAAI,CAACd,YAAY,CAACc,CAAC,CAAC,CAACD,GAAG,CAACJ,IAAI,CAAC,EAAE,OAAO,IAAI;MACjD;IACF;IACA,OAAO,KAAK;EACd;EAEAM,WAAWA,CAACN,IAAY,EAAEO,WAAyB,EAAEC,GAAa,EAAE;IAClE,IAAID,WAAW,GAAGE,gCAAoB,EAAE;MACtC,IAAI,IAAI,CAACV,SAAS,CAACC,IAAI,EAAE,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACU,MAAM,CAACC,KAAK,CAACC,kBAAM,CAACC,gBAAgB,EAAE;UACzCC,EAAE,EAAEN,GAAG;UACPO,cAAc,EAAEf;QAClB,CAAC,CAAC;MACJ;MACA,IAAI,CAACT,YAAY,CAAC,IAAI,CAACA,YAAY,CAACY,MAAM,GAAG,CAAC,CAAC,CAACa,GAAG,CAAChB,IAAI,CAAC;MACzD;IACF;IAEA,MAAMiB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACjC,IAAIX,WAAW,GAAGY,qCAAyB,EAAE;MAC3C,IAAI,CAACC,kBAAkB,CAACH,KAAK,EAAEjB,IAAI,CAAC;MACpCiB,KAAK,CAAC7B,kBAAkB,CAAC4B,GAAG,CAAChB,IAAI,CAAC;MAClC;IACF;IAEA,KAAK,CAACM,WAAW,CAACN,IAAI,EAAEO,WAAW,EAAEC,GAAG,CAAC;IAEzC,IAAID,WAAW,GAAGc,0BAAc,EAAE;MAChC,IAAI,EAAEd,WAAW,GAAGe,2BAAe,CAAC,EAAE;QAEpC,IAAI,CAACC,yBAAyB,CAACN,KAAK,EAAEjB,IAAI,EAAEO,WAAW,EAAEC,GAAG,CAAC;QAC7D,IAAI,CAACY,kBAAkB,CAACH,KAAK,EAAEjB,IAAI,CAAC;MACtC;MACAiB,KAAK,CAAClC,KAAK,CAACiC,GAAG,CAAChB,IAAI,CAAC;IACvB;IACA,IAAIO,WAAW,GAAGiB,8BAAkB,EAAEP,KAAK,CAAChC,KAAK,CAAC+B,GAAG,CAAChB,IAAI,CAAC;IAC3D,IAAIO,WAAW,GAAGkB,oCAAwB,EAAER,KAAK,CAAC/B,UAAU,CAAC8B,GAAG,CAAChB,IAAI,CAAC;IACtE,IAAIO,WAAW,GAAGmB,4BAAgB,EAAET,KAAK,CAAC9B,OAAO,CAAC6B,GAAG,CAAChB,IAAI,CAAC;EAC7D;EAEA2B,mBAAmBA,CACjBV,KAAsB,EACtBjB,IAAY,EACZO,WAAyB,EAChB;IACT,IAAIU,KAAK,CAAChC,KAAK,CAACmB,GAAG,CAACJ,IAAI,CAAC,EAAE;MACzB,IAAIO,WAAW,GAAGiB,8BAAkB,EAAE;QAGpC,MAAMI,OAAO,GAAG,CAAC,EAAErB,WAAW,GAAGkB,oCAAwB,CAAC;QAC1D,MAAMI,QAAQ,GAAGZ,KAAK,CAAC/B,UAAU,CAACkB,GAAG,CAACJ,IAAI,CAAC;QAC3C,OAAO4B,OAAO,KAAKC,QAAQ;MAC7B;MACA,OAAO,IAAI;IACb;IACA,IAAItB,WAAW,GAAGmB,4BAAgB,IAAIT,KAAK,CAAC9B,OAAO,CAACiB,GAAG,CAACJ,IAAI,CAAC,EAAE;MAC7D,IAAIiB,KAAK,CAACa,OAAO,CAAC1B,GAAG,CAACJ,IAAI,CAAC,EAAE;QAE3B,OAAO,CAAC,EAAEO,WAAW,GAAGe,2BAAe,CAAC;MAC1C,CAAC,MAAM;QAEL,OAAO,KAAK;MACd;IACF;IACA,IAAIf,WAAW,GAAGc,0BAAc,IAAIJ,KAAK,CAAClC,KAAK,CAACqB,GAAG,CAACJ,IAAI,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,OAAO,KAAK,CAAC2B,mBAAmB,CAACV,KAAK,EAAEjB,IAAI,EAAEO,WAAW,CAAC;EAC5D;EAEAwB,gBAAgBA,CAACC,EAAgB,EAAE;IACjC,MAAM;MAAEhC;IAAK,CAAC,GAAGgC,EAAE;IAEnB,IAAI,IAAI,CAACjC,SAAS,CAACC,IAAI,CAAC,EAAE;IAE1B,MAAME,GAAG,GAAG,IAAI,CAAC+B,UAAU,CAAC9B,MAAM;IAClC,KAAK,IAAIE,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjC,MAAMY,KAAK,GAAG,IAAI,CAACgB,UAAU,CAAC5B,CAAC,CAAC;MAChC,IAAIY,KAAK,CAAClC,KAAK,CAACqB,GAAG,CAACJ,IAAI,CAAC,IAAIiB,KAAK,CAAC7B,kBAAkB,CAACgB,GAAG,CAACJ,IAAI,CAAC,EAAE;IACnE;IAEA,KAAK,CAAC+B,gBAAgB,CAACC,EAAE,CAAC;EAC5B;AACF;AAACE,OAAA,CAAAC,OAAA,GAAA9C,sBAAA"}