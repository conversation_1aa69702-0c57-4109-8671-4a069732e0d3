"""
星圖生成工具 - 將AstroMCP輸出轉換為astrochart格式並生成SVG星圖
Chart Generator - Convert AstroMCP output to astrochart format and generate SVG charts
"""

import json
import re
import subprocess
import tempfile
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from langchain_core.tools import tool


class ChartGenerator:
    """星圖生成器類"""
    
    def __init__(self):
        """初始化星圖生成器"""
        self.astrochart_path = Path("astrochart/dist/astrochart.js")
        self.planet_mapping = {
            "Sun": "Sun",
            "Moon": "Moon", 
            "Mercury": "Mercury",
            "Venus": "Venus",
            "Mars": "Mars",
            "Jupiter": "Jupiter",
            "Saturn": "Saturn",
            "Uranus": "Uranus",
            "Neptune": "Neptune",
            "Pluto": "Pluto",
            "North Node": "NNode",
            "South Node": "SNode",
            "Chiron": "Chiron",
            "Lilith": "Lilith"
        }
    
    def parse_astro_text(self, astro_text: str) -> Dict:
        """
        解析AstroMCP的文本輸出，提取星體位置和宮位信息
        
        Args:
            astro_text (str): AstroMCP的文本輸出
            
        Returns:
            Dict: 包含planets和cusps的字典
        """
        planets = {}
        cusps = [0] * 12  # 12個宮位
        
        try:
            # 解析星體位置 (例如: "Sun is at 10° Capricorn")
            planet_pattern = r"(\w+(?:\s+\w+)*) is at (\d+)° (\w+)"
            planet_matches = re.findall(planet_pattern, astro_text)
            
            for planet_name, degree, sign in planet_matches:
                if planet_name in self.planet_mapping:
                    # 轉換星座為度數 (每個星座30度)
                    sign_degrees = self._sign_to_degrees(sign)
                    total_degrees = sign_degrees + int(degree)
                    planets[self.planet_mapping[planet_name]] = [total_degrees]
            
            # 解析上升星座 (Ascendant)
            asc_pattern = r"Ascendant is at (\d+)° (\w+)"
            asc_match = re.search(asc_pattern, astro_text)
            if asc_match:
                degree, sign = asc_match.groups()
                sign_degrees = self._sign_to_degrees(sign)
                asc_degrees = sign_degrees + int(degree)
                cusps[0] = asc_degrees  # 第一宮宮頭
                
                # 計算其他宮位 (簡化計算，每宮30度)
                for i in range(1, 12):
                    cusps[i] = (asc_degrees + i * 30) % 360
            
            # 如果沒有找到上升星座，使用默認值
            if cusps[0] == 0:
                cusps = [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
            
            return {
                "planets": planets,
                "cusps": cusps
            }
            
        except Exception as e:
            print(f"解析占星文本時發生錯誤: {e}")
            # 返回默認數據
            return {
                "planets": {
                    "Sun": [0],
                    "Moon": [90],
                    "Mercury": [30],
                    "Venus": [60]
                },
                "cusps": [0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330]
            }
    
    def _sign_to_degrees(self, sign: str) -> int:
        """
        將星座名稱轉換為度數
        
        Args:
            sign (str): 星座名稱
            
        Returns:
            int: 對應的度數
        """
        signs = {
            "Aries": 0, "Taurus": 30, "Gemini": 60, "Cancer": 90,
            "Leo": 120, "Virgo": 150, "Libra": 180, "Scorpio": 210,
            "Sagittarius": 240, "Capricorn": 270, "Aquarius": 300, "Pisces": 330
        }
        return signs.get(sign, 0)
    
    def generate_chart_html(self, chart_data: Dict, title: str = "星盤圖") -> str:
        """
        生成包含星圖的HTML代碼
        
        Args:
            chart_data (Dict): 星圖數據
            title (str): 圖表標題
            
        Returns:
            str: HTML代碼
        """
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="utf-8">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }}
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }}
        h1 {{
            color: #4a5568;
            margin-bottom: 20px;
        }}
        #paper {{
            margin: 0 auto;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
        }}
    </style>
</head>
<body>
    <div class="chart-container">
        <h1>{title}</h1>
        <div id="paper"></div>
    </div>
    
    <script src="astrochart/dist/astrochart.js"></script>
    <script type="text/javascript">
        var data = {json.dumps(chart_data, ensure_ascii=False)};
        
        window.onload = function () {{
            try {{
                var chart = new astrochart.Chart('paper', 500, 500).radix(data);
                
                // 添加重要點位
                chart.addPointsOfInterest({{
                    "As": [data.cusps[0]],
                    "Ic": [data.cusps[3]], 
                    "Ds": [data.cusps[6]],
                    "Mc": [data.cusps[9]]
                }});
                
                // 計算相位
                chart.aspects();
            }} catch (error) {{
                console.error('星圖生成錯誤:', error);
                document.getElementById('paper').innerHTML = 
                    '<p style="color: red;">星圖生成失敗: ' + error.message + '</p>';
            }}
        }};
    </script>
</body>
</html>"""
        return html_template
    
    def save_chart(self, chart_data: Dict, filename: str = "chart.html") -> str:
        """
        保存星圖為HTML文件
        
        Args:
            chart_data (Dict): 星圖數據
            filename (str): 文件名
            
        Returns:
            str: 保存的文件路徑
        """
        try:
            html_content = self.generate_chart_html(chart_data)
            
            # 確保charts目錄存在
            charts_dir = Path("charts")
            charts_dir.mkdir(exist_ok=True)
            
            # 保存文件
            file_path = charts_dir / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return str(file_path.absolute())
            
        except Exception as e:
            print(f"保存星圖時發生錯誤: {e}")
            return ""


# 創建全局星圖生成器實例
_chart_generator = ChartGenerator()


@tool("generate_astrology_chart")
def generate_astrology_chart(astro_text: str, title: str = "個人星盤") -> str:
    """
    根據占星文本生成星圖
    
    這個工具可以將AstroMCP工具的輸出文本轉換為可視化的星圖。
    適用於將星體位置數據轉換為圖形化的星盤顯示。
    
    Args:
        astro_text (str): AstroMCP工具的輸出文本，包含星體位置信息
        title (str): 星圖標題，默認為"個人星盤"
        
    Returns:
        str: 生成的星圖HTML文件路徑和基本信息
        
    Examples:
        - generate_astrology_chart("Sun is at 10° Capricorn. Moon is at 21° Pisces...")
        - generate_astrology_chart(astro_data, "1992年12月18日星盤")
    """
    try:
        # 解析占星文本
        chart_data = _chart_generator.parse_astro_text(astro_text)
        
        # 生成文件名
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"chart_{timestamp}.html"
        
        # 保存星圖
        file_path = _chart_generator.save_chart(chart_data, filename)
        
        if file_path:
            # 統計信息
            planet_count = len(chart_data["planets"])
            
            result = f"""
🌟 星圖生成成功！

📊 星圖信息：
- 標題：{title}
- 星體數量：{planet_count}個
- 文件路徑：{file_path}

📋 包含的星體：
{', '.join(chart_data["planets"].keys())}

💡 使用說明：
1. 在瀏覽器中打開生成的HTML文件即可查看星圖
2. 星圖包含星體位置、宮位劃分和主要相位
3. 文件保存在charts目錄中，可以分享或保存

🔗 文件位置：{file_path}
"""
            return result
        else:
            return "❌ 星圖生成失敗，請檢查輸入數據格式"
            
    except Exception as e:
        return f"❌ 星圖生成時發生錯誤：{str(e)}"


@tool("parse_chart_data")
def parse_chart_data(astro_text: str) -> str:
    """
    解析占星文本數據
    
    這個工具用於測試和調試，可以查看從占星文本中提取的結構化數據。
    
    Args:
        astro_text (str): 占星文本
        
    Returns:
        str: 解析後的結構化數據
    """
    try:
        chart_data = _chart_generator.parse_astro_text(astro_text)
        
        result = f"""
📊 解析結果：

🪐 星體位置：
"""
        for planet, position in chart_data["planets"].items():
            result += f"- {planet}: {position[0]}°\n"
        
        result += f"""
🏠 宮位劃分：
"""
        for i, cusp in enumerate(chart_data["cusps"], 1):
            result += f"- 第{i}宮: {cusp}°\n"
        
        return result
        
    except Exception as e:
        return f"❌ 解析失敗：{str(e)}"


# 導出工具列表
CHART_TOOLS = [
    generate_astrology_chart,
    parse_chart_data
]


def get_chart_tools() -> List:
    """
    獲取所有星圖工具
    
    Returns:
        List: 星圖工具列表
    """
    return CHART_TOOLS


if __name__ == "__main__":
    # 測試星圖生成
    test_text = """
Astrology Chart (location: New York, USA, at: 1/1/2001, 1:01:00 AM):

Ascendant is at 21° Libra. Sun is at 10° Capricorn. Moon is at 21° Pisces. Mercury is at 14° Capricorn. Venus is at 27° Aquarius. Mars is at 5° Scorpio. Jupiter is at 2° Gemini. Saturn is at 24° Taurus. Uranus is at 18° Aquarius. Neptune is at 5° Aquarius. Pluto is at 13° Sagittarius.
"""
    
    print("測試星圖生成...")
    result = generate_astrology_chart(test_text, "測試星盤")
    print(result)
