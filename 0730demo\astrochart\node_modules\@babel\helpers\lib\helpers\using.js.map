{"version": 3, "names": ["_using", "stack", "value", "isAwait", "TypeError", "dispose", "Symbol", "asyncDispose", "for", "push", "v", "d", "a"], "sources": ["../../src/helpers/using.js"], "sourcesContent": ["/* @minVersion 7.22.0 */\n\nexport default function _using(stack, value, isAwait) {\n  if (value === null || value === void 0) return value;\n  if (typeof value !== \"object\") {\n    throw new TypeError(\n      \"using declarations can only be used with objects, null, or undefined.\"\n    );\n  }\n  // core-js-pure uses Symbol.for for polyfilling well-known symbols\n  if (isAwait) {\n    var dispose =\n      value[Symbol.asyncDispose || Symbol.for(\"Symbol.asyncDispose\")];\n  }\n  if (dispose === null || dispose === void 0) {\n    dispose = value[Symbol.dispose || Symbol.for(\"Symbol.dispose\")];\n  }\n  if (typeof dispose !== \"function\") {\n    throw new TypeError(`Property [Symbol.dispose] is not a function.`);\n  }\n  stack.push({ v: value, d: dispose, a: isAwait });\n  return value;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EACpD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE,OAAOA,KAAK;EACpD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIE,SAAS,CACjB,uEACF,CAAC;EACH;EAEA,IAAID,OAAO,EAAE;IACX,IAAIE,OAAO,GACTH,KAAK,CAACI,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,GAAG,CAAC,qBAAqB,CAAC,CAAC;EACnE;EACA,IAAIH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAC1CA,OAAO,GAAGH,KAAK,CAACI,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAC,CAAC;EACjE;EACA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAID,SAAS,CAAE,8CAA6C,CAAC;EACrE;EACAH,KAAK,CAACQ,IAAI,CAAC;IAAEC,CAAC,EAAER,KAAK;IAAES,CAAC,EAAEN,OAAO;IAAEO,CAAC,EAAET;EAAQ,CAAC,CAAC;EAChD,OAAOD,KAAK;AACd"}