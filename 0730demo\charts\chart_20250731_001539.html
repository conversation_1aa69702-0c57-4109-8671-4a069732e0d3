
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="utf-8">
    <title>星盤圖</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', <PERSON><PERSON>, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #4a5568;
            margin-bottom: 20px;
        }
        #paper {
            margin: 0 auto;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <h1>星盤圖</h1>
        <div id="paper"></div>
    </div>
    
    <script src="astrochart/dist/astrochart.js"></script>
    <script type="text/javascript">
        var data = {"planets": {"Sun": [280], "Moon": [351], "Mercury": [284], "Venus": [327], "Mars": [215], "Jupiter": [62], "Saturn": [54], "Uranus": [318], "Neptune": [305], "Pluto": [253]}, "cusps": [201, 231, 261, 291, 321, 351, 21, 51, 81, 111, 141, 171]};
        
        window.onload = function () {
            try {
                var chart = new astrochart.Chart('paper', 500, 500).radix(data);
                
                // 添加重要點位
                chart.addPointsOfInterest({
                    "As": [data.cusps[0]],
                    "Ic": [data.cusps[3]], 
                    "Ds": [data.cusps[6]],
                    "Mc": [data.cusps[9]]
                });
                
                // 計算相位
                chart.aspects();
            } catch (error) {
                console.error('星圖生成錯誤:', error);
                document.getElementById('paper').innerHTML = 
                    '<p style="color: red;">星圖生成失敗: ' + error.message + '</p>';
            }
        };
    </script>
</body>
</html>