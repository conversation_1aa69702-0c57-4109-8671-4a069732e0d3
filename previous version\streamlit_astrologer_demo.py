#!/usr/bin/env python3
"""
Streamlit Demo Interface for AI Astrologer System
Simple web interface showcasing RAG-enhanced astrology consultations.
"""

import streamlit as st
import asyncio
import json
from datetime import datetime
from astrologer_prototype import AstrologerPrototype

# Configure Streamlit page
st.set_page_config(
    page_title="🌟 AI Astrologer Demo",
    page_icon="🔮",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for astrology theme
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #4A5568;
        font-size: 2.5rem;
        margin-bottom: 2rem;
    }
    .rag-chunk {
        background-color: #F7FAFC;
        border-left: 4px solid #4299E1;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0.5rem;
    }
    .prompt-section {
        background-color: #FFF5F5;
        border: 1px solid #FEB2B2;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .system-status {
        background-color: #F0FFF4;
        border: 1px solid #9AE6B4;
        padding: 1rem;
        border-radius: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'astrologer' not in st.session_state:
    st.session_state.astrologer = None
if 'conversation_history' not in st.session_state:
    st.session_state.conversation_history = []
if 'current_threshold' not in st.session_state:
    st.session_state.current_threshold = 0.7
if 'rag_enabled' not in st.session_state:
    st.session_state.rag_enabled = True
if 'session_id' not in st.session_state:
    st.session_state.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
if 'input_key' not in st.session_state:
    st.session_state.input_key = 0

def initialize_astrologer(threshold):
    """Initialize astrologer with given threshold."""
    try:
        st.session_state.astrologer = AstrologerPrototype(similarity_threshold=threshold)
        return True
    except Exception as e:
        st.error(f"Failed to initialize astrologer: {e}")
        return False

async def get_astrology_response(question, enable_rag=True):
    """Get response from astrologer."""
    if st.session_state.astrologer is None:
        return None

    try:
        result = await st.session_state.astrologer.get_astrology_response(question, enable_rag)
        return result
    except Exception as e:
        st.error(f"Error getting response: {e}")
        return None

def add_to_conversation(question, result):
    """Add a question-response pair to the conversation history."""
    conversation_entry = {
        "timestamp": datetime.now().strftime("%H:%M:%S"),
        "question": question,
        "response": result['response'],
        "rag_info": result['rag_info'],
        "prompt_data": result.get('prompt_data', {}),
        "system_info": {
            "threshold": result['similarity_threshold'],
            "has_rag_context": result['has_rag_context'],
            "response_length": len(result['response'])
        }
    }
    st.session_state.conversation_history.append(conversation_entry)

def clear_conversation():
    """Clear the conversation history."""
    st.session_state.conversation_history = []
    st.session_state.input_key += 1  # Force input field refresh

def display_conversation_history():
    """Display the full conversation history in a chat-like format."""
    if not st.session_state.conversation_history:
        st.info("💬 Start a conversation by asking an astrology question below!")
        return

    st.subheader(f"💬 Conversation History ({len(st.session_state.conversation_history)} exchanges)")

    # Create a scrollable container for chat history
    chat_container = st.container()

    with chat_container:
        for i, entry in enumerate(st.session_state.conversation_history):
            # User question
            st.markdown(f"""
            <div style="background-color: #E3F2FD; padding: 10px; border-radius: 10px; margin: 5px 0; border-left: 4px solid #2196F3;">
                <strong>🤔 You ({entry['timestamp']}):</strong><br>
                {entry['question']}
            </div>
            """, unsafe_allow_html=True)

            # Astrologer response
            st.markdown(f"""
            <div style="background-color: #F3E5F5; padding: 10px; border-radius: 10px; margin: 5px 0; border-left: 4px solid #9C27B0;">
                <strong>🔮 AI Astrologer:</strong><br>
                {entry['response'][:300]}{'...' if len(entry['response']) > 300 else ''}
            </div>
            """, unsafe_allow_html=True)

            # Show RAG info for this exchange
            rag_info = entry['rag_info']
            if rag_info['matches_used'] > 0:
                st.caption(f"📊 RAG: {rag_info['matches_used']}/{rag_info['matches_found']} chunks used | Threshold: {entry['system_info']['threshold']}")
            else:
                st.caption(f"⚠️ Fallback mode | Threshold: {entry['system_info']['threshold']}")

            # Add expandable full response
            with st.expander(f"View full response #{i+1}"):
                st.markdown(entry['response'])

                # Show detailed RAG info
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Matches Found", rag_info['matches_found'])
                with col2:
                    st.metric("Matches Used", rag_info['matches_used'])
                with col3:
                    st.metric("Response Length", f"{entry['system_info']['response_length']} chars")

            st.markdown("---")

def display_rag_chunks(chunks):
    """Display RAG chunks in a formatted way."""
    if not chunks:
        st.info("No RAG chunks retrieved")
        return

    st.subheader(f"📚 Retrieved Knowledge ({len(chunks)} chunks)")

    for i, chunk in enumerate(chunks, 1):
        metadata = chunk.get('metadata', {})
        content = metadata.get('chunk_text', 'No content available')
        source = metadata.get('source_file', 'Unknown source')
        score = chunk.get('score', 0.0)

        with st.expander(f"Chunk {i} - Score: {score:.3f} - Source: {source}"):
            st.markdown(f'<div class="rag-chunk">{content}</div>', unsafe_allow_html=True)

def display_prompt_breakdown(prompt_data):
    """Display the complete prompt breakdown."""
    st.subheader("🔍 Prompt Transparency")
    
    # Tabs for different views
    tab1, tab2 = st.tabs(["📋 Sections", "📄 Complete Prompt"])
    
    with tab1:
        # Base Persona
        with st.expander("🎭 Base Astrologer Persona", expanded=True):
            st.markdown(f'<div class="prompt-section">{prompt_data["base_persona"]}</div>', 
                       unsafe_allow_html=True)
        
        # RAG Context
        if prompt_data["rag_context"]:
            with st.expander("🔍 RAG Context", expanded=False):
                st.markdown(f'<div class="prompt-section">{prompt_data["rag_context"]}</div>', 
                           unsafe_allow_html=True)
        else:
            st.info("No RAG context included (fallback mode)")
        
        # User Question
        with st.expander("❓ User Question"):
            st.markdown(f'<div class="prompt-section">{prompt_data["user_question"]}</div>', 
                       unsafe_allow_html=True)
    
    with tab2:
        # Complete prompt
        st.text_area(
            "Complete System Prompt",
            value=prompt_data["final_system_prompt"],
            height=400,
            disabled=True
        )
    
    # Stats
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Word Count", prompt_data["word_count"])
    with col2:
        st.metric("Estimated Tokens", prompt_data["estimated_tokens"])
    with col3:
        st.metric("Character Count", len(prompt_data["final_system_prompt"]))

def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🌟 AI Astrologer Demo 🔮</h1>', unsafe_allow_html=True)
    st.markdown("*Explore RAG-enhanced astrological consultations with complete transparency*")
    
    # Sidebar for settings
    with st.sidebar:
        st.header("⚙️ System Settings")

        # Session info
        st.info(f"🆔 Session: {st.session_state.session_id}")

        # Similarity threshold
        threshold = st.slider(
            "Similarity Threshold",
            min_value=0.0,
            max_value=1.0,
            value=st.session_state.current_threshold,
            step=0.05,
            help="Minimum similarity score for RAG chunks",
            key="threshold_slider"
        )

        # Update session state when threshold changes
        if threshold != st.session_state.current_threshold:
            st.session_state.current_threshold = threshold
            if st.session_state.astrologer:
                st.session_state.astrologer.similarity_threshold = threshold

        # RAG toggle
        enable_rag = st.checkbox(
            "Enable RAG",
            value=st.session_state.rag_enabled,
            help="Use retrieval-augmented generation",
            key="rag_checkbox"
        )
        st.session_state.rag_enabled = enable_rag

        # Initialize button
        if st.button("🚀 Initialize/Restart Astrologer"):
            with st.spinner("Initializing..."):
                if initialize_astrologer(threshold):
                    st.success("Astrologer initialized!")
                    st.rerun()

        # Conversation controls
        st.markdown("---")
        st.subheader("💬 Conversation Controls")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ Clear Chat", help="Clear conversation history"):
                clear_conversation()
                st.rerun()

        with col2:
            conversation_count = len(st.session_state.conversation_history)
            st.metric("Exchanges", conversation_count)

        # System status
        st.markdown("---")
        if st.session_state.astrologer:
            st.markdown('<div class="system-status">', unsafe_allow_html=True)
            st.success("✅ System Ready")
            st.write(f"**Threshold:** {threshold}")
            st.write(f"**RAG Enabled:** {enable_rag}")
            st.write(f"**Conversations:** {len(st.session_state.conversation_history)}")
            st.markdown('</div>', unsafe_allow_html=True)
        else:
            st.warning("⚠️ Please initialize the astrologer system")
    
    # Main content area
    if st.session_state.astrologer is None:
        st.warning("Please initialize the astrologer system using the sidebar.")
        return

    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["💬 Conversation", "🔍 Latest Analysis", "📋 Prompt Inspector"])

    with tab1:
        # Continuous chat interface
        st.header("💬 Continuous Astrology Consultation")

        # Display conversation history
        display_conversation_history()

        # Input area for new questions
        st.markdown("---")
        st.subheader("Ask your next question:")

        # Use form for better UX
        with st.form(key=f"question_form_{st.session_state.input_key}", clear_on_submit=True):
            question = st.text_input(
                "Your astrology question:",
                placeholder="e.g., What does Mars in the 7th house mean for relationships?",
                key=f"question_input_{st.session_state.input_key}"
            )

            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                submit_button = st.form_submit_button("🔮 Ask Astrologer", type="primary")
            with col2:
                if st.form_submit_button("🎲 Random Question"):
                    # Provide some sample questions
                    sample_questions = [
                        "What does my Sun sign reveal about my personality?",
                        "How do Mercury retrogrades affect communication?",
                        "What is the significance of my rising sign?",
                        "How do Saturn transits influence personal growth?",
                        "What does a Grand Trine mean in astrology?",
                        "How do Venus aspects affect relationships?",
                        "What is the meaning of the North Node in my chart?",
                        "How do lunar phases impact emotions?"
                    ]
                    import random
                    question = random.choice(sample_questions)
                    submit_button = True

            with col3:
                if st.form_submit_button("💡 Help"):
                    st.info("""
                    **Tips for better readings:**
                    - Be specific about what you want to know
                    - Ask about planetary placements, aspects, or transits
                    - Build on previous questions in the conversation
                    - Try different similarity thresholds to see how RAG affects responses
                    """)

        # Process the question
        if submit_button and question:
            with st.spinner("🌟 Consulting the stars..."):
                # Get response
                result = asyncio.run(get_astrology_response(question, st.session_state.rag_enabled))

                if result:
                    # Add to conversation history
                    add_to_conversation(question, result)

                    # Increment input key to refresh form
                    st.session_state.input_key += 1

                    # Show success message and rerun to display new conversation
                    st.success(f"✨ Response received! ({len(result['response'])} characters)")
                    st.rerun()
    
    with tab2:
        # Latest analysis tab - show detailed info about the most recent exchange
        if st.session_state.conversation_history:
            latest = st.session_state.conversation_history[-1]

            st.header("🔍 Latest Exchange Analysis")

            # Question and response
            st.subheader("❓ Question")
            st.info(latest['question'])

            st.subheader("🔮 Response")
            st.markdown(latest['response'])

            # Create columns for metrics
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("RAG Matches Found", latest['rag_info']['matches_found'])
            with col2:
                st.metric("RAG Matches Used", latest['rag_info']['matches_used'])
            with col3:
                st.metric("Similarity Threshold", latest['system_info']['threshold'])
            with col4:
                st.metric("Response Length", f"{latest['system_info']['response_length']} chars")

            # RAG status
            if latest['system_info']['has_rag_context']:
                st.success("✅ RAG was active for this response")
            else:
                st.warning("⚠️ Fallback mode was used (no RAG context)")

            # RAG context display
            if latest['rag_info']['matches_used'] > 0:
                st.subheader("📚 Knowledge Sources Used")
                if 'rag_context' in latest['prompt_data']:
                    st.text_area("RAG Context", latest['prompt_data']['rag_context'], height=200, disabled=True)
        else:
            st.info("No conversation yet. Start by asking a question in the Conversation tab!")

    with tab3:
        # Prompt inspector tab
        if st.session_state.conversation_history:
            latest = st.session_state.conversation_history[-1]

            st.header("📋 Prompt Transparency - Latest Exchange")

            if 'prompt_data' in latest:
                display_prompt_breakdown(latest['prompt_data'])
            else:
                st.warning("No prompt data available for the latest exchange")
        else:
            st.info("No conversation yet. Start by asking a question to see prompt transparency!")

if __name__ == "__main__":
    main()
