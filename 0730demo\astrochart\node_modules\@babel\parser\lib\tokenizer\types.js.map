{"version": 3, "names": ["_context", "require", "beforeExpr", "startsExpr", "isLoop", "isAssign", "prefix", "postfix", "ExportedTokenType", "constructor", "label", "conf", "keyword", "rightAssociative", "binop", "updateContext", "exports", "keywords", "Map", "createKeyword", "name", "options", "token", "createToken", "set", "createBinop", "tokenTypeCounter", "tokenTypes", "tokenLabels", "tokenBinops", "tokenBeforeExprs", "tokenStartsExprs", "tokenPrefixes", "_options$binop", "_options$beforeExpr", "_options$startsExpr", "_options$prefix", "push", "createKeywordLike", "_options$binop2", "_options$beforeExpr2", "_options$startsExpr2", "_options$prefix2", "tt", "bracketL", "bracketHashL", "bracketBarL", "bracketR", "bracketBarR", "braceL", "braceBarL", "braceHashL", "braceR", "braceBarR", "parenL", "parenR", "comma", "semi", "colon", "doubleColon", "dot", "question", "questionDot", "arrow", "template", "ellipsis", "backQuote", "dollarBraceL", "templateTail", "templateNonTail", "at", "hash", "interpreterDirective", "eq", "assign", "slashAssign", "xorAssign", "moduloAssign", "incDec", "bang", "tilde", "doubleCaret", "doubleAt", "pipeline", "nullishCoalescing", "logicalOR", "logicalAND", "bitwiseOR", "bitwiseXOR", "bitwiseAND", "equality", "lt", "gt", "relational", "bitShift", "bitShiftL", "bitShiftR", "plusMin", "modulo", "star", "slash", "exponent", "_in", "_instanceof", "_break", "_case", "_catch", "_continue", "_debugger", "_default", "_else", "_finally", "_function", "_if", "_return", "_switch", "_throw", "_try", "_var", "_const", "_with", "_new", "_this", "_super", "_class", "_extends", "_export", "_import", "_null", "_true", "_false", "_typeof", "_void", "_delete", "_do", "_for", "_while", "_as", "_assert", "_async", "_await", "_from", "_get", "_let", "_meta", "_of", "_sent", "_set", "_static", "_using", "_yield", "_asserts", "_checks", "_exports", "_global", "_implements", "_intrinsic", "_infer", "_is", "_mixins", "_proto", "_require", "_satisfies", "_keyof", "_readonly", "_unique", "_abstract", "_declare", "_enum", "_module", "_namespace", "_interface", "_type", "_opaque", "string", "num", "bigint", "decimal", "regexp", "privateName", "eof", "jsxName", "jsxText", "jsxTagStart", "jsxTagEnd", "placeholder", "tokenIsIdentifier", "tokenKeywordOrIdentifierIsKeyword", "tokenIsKeywordOrIdentifier", "tokenIsLiteralPropertyName", "tokenComesBeforeExpression", "tokenCanStartExpression", "tokenIsAssignment", "tokenIsFlowInterfaceOrTypeOrOpaque", "tokenIsLoop", "tokenIsKeyword", "tokenIsOperator", "tokenIsPostfix", "tokenIsPrefix", "tokenIsTSTypeOperator", "tokenIsTSDeclarationStart", "tokenLabelName", "tokenOperatorPrecedence", "tokenIsBinaryOperator", "tokenIsRightAssociative", "tokenIsTemplate", "getExportedToken", "isTokenType", "obj", "context", "pop", "tc", "brace", "length", "j_expr", "j_oTag"], "sources": ["../../src/tokenizer/types.ts"], "sourcesContent": ["import { types as tc, type TokContext } from \"./context\";\n// ## Token types\n\n// The assignment of fine-grained, information-carrying type objects\n// allows the tokenizer to store the information it has about a\n// token in a way that is very cheap for the parser to look up.\n\n// All token type variables start with an underscore, to make them\n// easy to recognize.\n\n// The `beforeExpr` property is used to disambiguate between 1) binary\n// expression (<) and JSX Tag start (<name>); 2) object literal and JSX\n// texts. It is set on the `updateContext` function in the JSX plugin.\n\n// The `startsExpr` property is used to determine whether an expression\n// may be the “argument” subexpression of a `yield` expression or\n// `yield` statement. It is set on all token types that may be at the\n// start of a subexpression.\n\n// `isLoop` marks a keyword as starting a loop, which is important\n// to know when parsing a label, in order to allow or disallow\n// continue jumps to that label.\n\nconst beforeExpr = true;\nconst startsExpr = true;\nconst isLoop = true;\nconst isAssign = true;\nconst prefix = true;\nconst postfix = true;\n\ntype TokenOptions = {\n  keyword?: string;\n  beforeExpr?: boolean;\n  startsExpr?: boolean;\n  rightAssociative?: boolean;\n  isLoop?: boolean;\n  isAssign?: boolean;\n  prefix?: boolean;\n  postfix?: boolean;\n  binop?: number | null;\n};\n\n// Internally the tokenizer stores token as a number\nexport type TokenType = number;\n\n// The `ExportedTokenType` is exported via `tokTypes` and accessible\n// when `tokens: true` is enabled. Unlike internal token type, it provides\n// metadata of the tokens.\nexport class ExportedTokenType {\n  label: string;\n  keyword: string | undefined | null;\n  beforeExpr: boolean;\n  startsExpr: boolean;\n  rightAssociative: boolean;\n  isLoop: boolean;\n  isAssign: boolean;\n  prefix: boolean;\n  postfix: boolean;\n  binop: number | undefined | null;\n  // todo(Babel 8): remove updateContext from exposed token layout\n  declare updateContext:\n    | ((context: Array<TokContext>) => void)\n    | undefined\n    | null;\n\n  constructor(label: string, conf: TokenOptions = {}) {\n    this.label = label;\n    this.keyword = conf.keyword;\n    this.beforeExpr = !!conf.beforeExpr;\n    this.startsExpr = !!conf.startsExpr;\n    this.rightAssociative = !!conf.rightAssociative;\n    this.isLoop = !!conf.isLoop;\n    this.isAssign = !!conf.isAssign;\n    this.prefix = !!conf.prefix;\n    this.postfix = !!conf.postfix;\n    this.binop = conf.binop != null ? conf.binop : null;\n    if (!process.env.BABEL_8_BREAKING) {\n      this.updateContext = null;\n    }\n  }\n}\n\n// A map from keyword/keyword-like string value to the token type\nexport const keywords = new Map<string, TokenType>();\n\nfunction createKeyword(name: string, options: TokenOptions = {}): TokenType {\n  options.keyword = name;\n  const token = createToken(name, options);\n  keywords.set(name, token);\n  return token;\n}\n\nfunction createBinop(name: string, binop: number) {\n  return createToken(name, { beforeExpr, binop });\n}\n\nlet tokenTypeCounter = -1;\nexport const tokenTypes: ExportedTokenType[] = [];\nconst tokenLabels: string[] = [];\nconst tokenBinops: number[] = [];\nconst tokenBeforeExprs: boolean[] = [];\nconst tokenStartsExprs: boolean[] = [];\nconst tokenPrefixes: boolean[] = [];\n\nfunction createToken(name: string, options: TokenOptions = {}): TokenType {\n  ++tokenTypeCounter;\n  tokenLabels.push(name);\n  tokenBinops.push(options.binop ?? -1);\n  tokenBeforeExprs.push(options.beforeExpr ?? false);\n  tokenStartsExprs.push(options.startsExpr ?? false);\n  tokenPrefixes.push(options.prefix ?? false);\n  tokenTypes.push(new ExportedTokenType(name, options));\n\n  return tokenTypeCounter;\n}\n\nfunction createKeywordLike(\n  name: string,\n  options: TokenOptions = {},\n): TokenType {\n  ++tokenTypeCounter;\n  keywords.set(name, tokenTypeCounter);\n  tokenLabels.push(name);\n  tokenBinops.push(options.binop ?? -1);\n  tokenBeforeExprs.push(options.beforeExpr ?? false);\n  tokenStartsExprs.push(options.startsExpr ?? false);\n  tokenPrefixes.push(options.prefix ?? false);\n  // In the exported token type, we set the label as \"name\" for backward compatibility with Babel 7\n  tokenTypes.push(new ExportedTokenType(\"name\", options));\n\n  return tokenTypeCounter;\n}\n\n// For performance the token type helpers depend on the following declarations order.\n// When adding new token types, please also check if the token helpers need update.\n\nexport type InternalTokenTypes = typeof tt;\n\nexport const tt = {\n  // Punctuation token types.\n  bracketL: createToken(\"[\", { beforeExpr, startsExpr }),\n  bracketHashL: createToken(\"#[\", { beforeExpr, startsExpr }),\n  bracketBarL: createToken(\"[|\", { beforeExpr, startsExpr }),\n  bracketR: createToken(\"]\"),\n  bracketBarR: createToken(\"|]\"),\n  braceL: createToken(\"{\", { beforeExpr, startsExpr }),\n  braceBarL: createToken(\"{|\", { beforeExpr, startsExpr }),\n  braceHashL: createToken(\"#{\", { beforeExpr, startsExpr }),\n  braceR: createToken(\"}\"),\n  braceBarR: createToken(\"|}\"),\n  parenL: createToken(\"(\", { beforeExpr, startsExpr }),\n  parenR: createToken(\")\"),\n  comma: createToken(\",\", { beforeExpr }),\n  semi: createToken(\";\", { beforeExpr }),\n  colon: createToken(\":\", { beforeExpr }),\n  doubleColon: createToken(\"::\", { beforeExpr }),\n  dot: createToken(\".\"),\n  question: createToken(\"?\", { beforeExpr }),\n  questionDot: createToken(\"?.\"),\n  arrow: createToken(\"=>\", { beforeExpr }),\n  template: createToken(\"template\"),\n  ellipsis: createToken(\"...\", { beforeExpr }),\n  backQuote: createToken(\"`\", { startsExpr }),\n  dollarBraceL: createToken(\"${\", { beforeExpr, startsExpr }),\n  // start: isTemplate\n  templateTail: createToken(\"...`\", { startsExpr }),\n  templateNonTail: createToken(\"...${\", { beforeExpr, startsExpr }),\n  // end: isTemplate\n  at: createToken(\"@\"),\n  hash: createToken(\"#\", { startsExpr }),\n\n  // Special hashbang token.\n  interpreterDirective: createToken(\"#!...\"),\n\n  // Operators. These carry several kinds of properties to help the\n  // parser use them properly (the presence of these properties is\n  // what categorizes them as operators).\n  //\n  // `binop`, when present, specifies that this operator is a binary\n  // operator, and will refer to its precedence.\n  //\n  // `prefix` and `postfix` mark the operator as a prefix or postfix\n  // unary operator.\n  //\n  // `isAssign` marks all of `=`, `+=`, `-=` etcetera, which act as\n  // binary operators with a very low precedence, that should result\n  // in AssignmentExpression nodes.\n\n  // start: isAssign\n  eq: createToken(\"=\", { beforeExpr, isAssign }),\n  assign: createToken(\"_=\", { beforeExpr, isAssign }),\n  slashAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  // These are only needed to support % and ^ as a Hack-pipe topic token.\n  // When the proposal settles on a token, the others can be merged with\n  // tt.assign.\n  xorAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  moduloAssign: createToken(\"_=\", { beforeExpr, isAssign }),\n  // end: isAssign\n\n  incDec: createToken(\"++/--\", { prefix, postfix, startsExpr }),\n  bang: createToken(\"!\", { beforeExpr, prefix, startsExpr }),\n  tilde: createToken(\"~\", { beforeExpr, prefix, startsExpr }),\n\n  // More possible topic tokens.\n  // When the proposal settles on a token, at least one of these may be removed.\n  doubleCaret: createToken(\"^^\", { startsExpr }),\n  doubleAt: createToken(\"@@\", { startsExpr }),\n\n  // start: isBinop\n  pipeline: createBinop(\"|>\", 0),\n  nullishCoalescing: createBinop(\"??\", 1),\n  logicalOR: createBinop(\"||\", 1),\n  logicalAND: createBinop(\"&&\", 2),\n  bitwiseOR: createBinop(\"|\", 3),\n  bitwiseXOR: createBinop(\"^\", 4),\n  bitwiseAND: createBinop(\"&\", 5),\n  equality: createBinop(\"==/!=/===/!==\", 6),\n  lt: createBinop(\"</>/<=/>=\", 7),\n  gt: createBinop(\"</>/<=/>=\", 7),\n  relational: createBinop(\"</>/<=/>=\", 7),\n  bitShift: createBinop(\"<</>>/>>>\", 8),\n  bitShiftL: createBinop(\"<</>>/>>>\", 8),\n  bitShiftR: createBinop(\"<</>>/>>>\", 8),\n  plusMin: createToken(\"+/-\", { beforeExpr, binop: 9, prefix, startsExpr }),\n  // startsExpr: required by v8intrinsic plugin\n  modulo: createToken(\"%\", { binop: 10, startsExpr }),\n  // unset `beforeExpr` as it can be `function *`\n  star: createToken(\"*\", { binop: 10 }),\n  slash: createBinop(\"/\", 10),\n  exponent: createToken(\"**\", {\n    beforeExpr,\n    binop: 11,\n    rightAssociative: true,\n  }),\n\n  // Keywords\n  // Don't forget to update packages/babel-helper-validator-identifier/src/keyword.js\n  // when new keywords are added\n  // start: isLiteralPropertyName\n  // start: isKeyword\n  _in: createKeyword(\"in\", { beforeExpr, binop: 7 }),\n  _instanceof: createKeyword(\"instanceof\", { beforeExpr, binop: 7 }),\n  // end: isBinop\n  _break: createKeyword(\"break\"),\n  _case: createKeyword(\"case\", { beforeExpr }),\n  _catch: createKeyword(\"catch\"),\n  _continue: createKeyword(\"continue\"),\n  _debugger: createKeyword(\"debugger\"),\n  _default: createKeyword(\"default\", { beforeExpr }),\n  _else: createKeyword(\"else\", { beforeExpr }),\n  _finally: createKeyword(\"finally\"),\n  _function: createKeyword(\"function\", { startsExpr }),\n  _if: createKeyword(\"if\"),\n  _return: createKeyword(\"return\", { beforeExpr }),\n  _switch: createKeyword(\"switch\"),\n  _throw: createKeyword(\"throw\", { beforeExpr, prefix, startsExpr }),\n  _try: createKeyword(\"try\"),\n  _var: createKeyword(\"var\"),\n  _const: createKeyword(\"const\"),\n  _with: createKeyword(\"with\"),\n  _new: createKeyword(\"new\", { beforeExpr, startsExpr }),\n  _this: createKeyword(\"this\", { startsExpr }),\n  _super: createKeyword(\"super\", { startsExpr }),\n  _class: createKeyword(\"class\", { startsExpr }),\n  _extends: createKeyword(\"extends\", { beforeExpr }),\n  _export: createKeyword(\"export\"),\n  _import: createKeyword(\"import\", { startsExpr }),\n  _null: createKeyword(\"null\", { startsExpr }),\n  _true: createKeyword(\"true\", { startsExpr }),\n  _false: createKeyword(\"false\", { startsExpr }),\n  _typeof: createKeyword(\"typeof\", { beforeExpr, prefix, startsExpr }),\n  _void: createKeyword(\"void\", { beforeExpr, prefix, startsExpr }),\n  _delete: createKeyword(\"delete\", { beforeExpr, prefix, startsExpr }),\n  // start: isLoop\n  _do: createKeyword(\"do\", { isLoop, beforeExpr }),\n  _for: createKeyword(\"for\", { isLoop }),\n  _while: createKeyword(\"while\", { isLoop }),\n  // end: isLoop\n  // end: isKeyword\n\n  // Primary literals\n  // start: isIdentifier\n  _as: createKeywordLike(\"as\", { startsExpr }),\n  _assert: createKeywordLike(\"assert\", { startsExpr }),\n  _async: createKeywordLike(\"async\", { startsExpr }),\n  _await: createKeywordLike(\"await\", { startsExpr }),\n  _from: createKeywordLike(\"from\", { startsExpr }),\n  _get: createKeywordLike(\"get\", { startsExpr }),\n  _let: createKeywordLike(\"let\", { startsExpr }),\n  _meta: createKeywordLike(\"meta\", { startsExpr }),\n  _of: createKeywordLike(\"of\", { startsExpr }),\n  _sent: createKeywordLike(\"sent\", { startsExpr }),\n  _set: createKeywordLike(\"set\", { startsExpr }),\n  _static: createKeywordLike(\"static\", { startsExpr }),\n  _using: createKeywordLike(\"using\", { startsExpr }),\n  _yield: createKeywordLike(\"yield\", { startsExpr }),\n\n  // Flow and TypeScript Keywordlike\n  _asserts: createKeywordLike(\"asserts\", { startsExpr }),\n  _checks: createKeywordLike(\"checks\", { startsExpr }),\n  _exports: createKeywordLike(\"exports\", { startsExpr }),\n  _global: createKeywordLike(\"global\", { startsExpr }),\n  _implements: createKeywordLike(\"implements\", { startsExpr }),\n  _intrinsic: createKeywordLike(\"intrinsic\", { startsExpr }),\n  _infer: createKeywordLike(\"infer\", { startsExpr }),\n  _is: createKeywordLike(\"is\", { startsExpr }),\n  _mixins: createKeywordLike(\"mixins\", { startsExpr }),\n  _proto: createKeywordLike(\"proto\", { startsExpr }),\n  _require: createKeywordLike(\"require\", { startsExpr }),\n  _satisfies: createKeywordLike(\"satisfies\", { startsExpr }),\n  // start: isTSTypeOperator\n  _keyof: createKeywordLike(\"keyof\", { startsExpr }),\n  _readonly: createKeywordLike(\"readonly\", { startsExpr }),\n  _unique: createKeywordLike(\"unique\", { startsExpr }),\n  // end: isTSTypeOperator\n  // start: isTSDeclarationStart\n  _abstract: createKeywordLike(\"abstract\", { startsExpr }),\n  _declare: createKeywordLike(\"declare\", { startsExpr }),\n  _enum: createKeywordLike(\"enum\", { startsExpr }),\n  _module: createKeywordLike(\"module\", { startsExpr }),\n  _namespace: createKeywordLike(\"namespace\", { startsExpr }),\n  // start: isFlowInterfaceOrTypeOrOpaque\n  _interface: createKeywordLike(\"interface\", { startsExpr }),\n  _type: createKeywordLike(\"type\", { startsExpr }),\n  // end: isTSDeclarationStart\n  _opaque: createKeywordLike(\"opaque\", { startsExpr }),\n  // end: isFlowInterfaceOrTypeOrOpaque\n  name: createToken(\"name\", { startsExpr }),\n  // end: isIdentifier\n\n  string: createToken(\"string\", { startsExpr }),\n  num: createToken(\"num\", { startsExpr }),\n  bigint: createToken(\"bigint\", { startsExpr }),\n  decimal: createToken(\"decimal\", { startsExpr }),\n  // end: isLiteralPropertyName\n  regexp: createToken(\"regexp\", { startsExpr }),\n  privateName: createToken(\"#name\", { startsExpr }),\n  eof: createToken(\"eof\"),\n\n  // jsx plugin\n  jsxName: createToken(\"jsxName\"),\n  jsxText: createToken(\"jsxText\", { beforeExpr: true }),\n  jsxTagStart: createToken(\"jsxTagStart\", { startsExpr: true }),\n  jsxTagEnd: createToken(\"jsxTagEnd\"),\n\n  // placeholder plugin\n  placeholder: createToken(\"%%\", { startsExpr: true }),\n} as const;\n\nexport function tokenIsIdentifier(token: TokenType): boolean {\n  return token >= tt._as && token <= tt.name;\n}\n\nexport function tokenKeywordOrIdentifierIsKeyword(token: TokenType): boolean {\n  // we can remove the token >= tt._in check when we\n  // know a token is either keyword or identifier\n  return token <= tt._while;\n}\n\nexport function tokenIsKeywordOrIdentifier(token: TokenType): boolean {\n  return token >= tt._in && token <= tt.name;\n}\n\nexport function tokenIsLiteralPropertyName(token: TokenType): boolean {\n  return token >= tt._in && token <= tt.decimal;\n}\n\nexport function tokenComesBeforeExpression(token: TokenType): boolean {\n  return tokenBeforeExprs[token];\n}\n\nexport function tokenCanStartExpression(token: TokenType): boolean {\n  return tokenStartsExprs[token];\n}\n\nexport function tokenIsAssignment(token: TokenType): boolean {\n  return token >= tt.eq && token <= tt.moduloAssign;\n}\n\nexport function tokenIsFlowInterfaceOrTypeOrOpaque(token: TokenType): boolean {\n  return token >= tt._interface && token <= tt._opaque;\n}\n\nexport function tokenIsLoop(token: TokenType): boolean {\n  return token >= tt._do && token <= tt._while;\n}\n\nexport function tokenIsKeyword(token: TokenType): boolean {\n  return token >= tt._in && token <= tt._while;\n}\n\nexport function tokenIsOperator(token: TokenType): boolean {\n  return token >= tt.pipeline && token <= tt._instanceof;\n}\n\nexport function tokenIsPostfix(token: TokenType): boolean {\n  return token === tt.incDec;\n}\n\nexport function tokenIsPrefix(token: TokenType): boolean {\n  return tokenPrefixes[token];\n}\n\nexport function tokenIsTSTypeOperator(token: TokenType): boolean {\n  return token >= tt._keyof && token <= tt._unique;\n}\n\nexport function tokenIsTSDeclarationStart(token: TokenType): boolean {\n  return token >= tt._abstract && token <= tt._type;\n}\n\nexport function tokenLabelName(token: TokenType): string {\n  return tokenLabels[token];\n}\n\nexport function tokenOperatorPrecedence(token: TokenType): number {\n  return tokenBinops[token];\n}\n\nexport function tokenIsBinaryOperator(token: TokenType): boolean {\n  return tokenBinops[token] !== -1;\n}\n\nexport function tokenIsRightAssociative(token: TokenType): boolean {\n  return token === tt.exponent;\n}\n\nexport function tokenIsTemplate(token: TokenType): boolean {\n  return token >= tt.templateTail && token <= tt.templateNonTail;\n}\n\nexport function getExportedToken(token: TokenType): ExportedTokenType {\n  return tokenTypes[token];\n}\n\nexport function isTokenType(obj: any): boolean {\n  return typeof obj === \"number\";\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  tokenTypes[tt.braceR].updateContext = context => {\n    context.pop();\n  };\n\n  tokenTypes[tt.braceL].updateContext =\n    tokenTypes[tt.braceHashL].updateContext =\n    tokenTypes[tt.dollarBraceL].updateContext =\n      context => {\n        context.push(tc.brace);\n      };\n\n  tokenTypes[tt.backQuote].updateContext = context => {\n    if (context[context.length - 1] === tc.template) {\n      context.pop();\n    } else {\n      context.push(tc.template);\n    }\n  };\n\n  tokenTypes[tt.jsxTagStart].updateContext = context => {\n    context.push(tc.j_expr, tc.j_oTag);\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAuBA,MAAMC,UAAU,GAAG,IAAI;AACvB,MAAMC,UAAU,GAAG,IAAI;AACvB,MAAMC,MAAM,GAAG,IAAI;AACnB,MAAMC,QAAQ,GAAG,IAAI;AACrB,MAAMC,MAAM,GAAG,IAAI;AACnB,MAAMC,OAAO,GAAG,IAAI;AAoBb,MAAMC,iBAAiB,CAAC;EAiB7BC,WAAWA,CAACC,KAAa,EAAEC,IAAkB,GAAG,CAAC,CAAC,EAAE;IAAA,KAhBpDD,KAAK;IAAA,KACLE,OAAO;IAAA,KACPV,UAAU;IAAA,KACVC,UAAU;IAAA,KACVU,gBAAgB;IAAA,KAChBT,MAAM;IAAA,KACNC,QAAQ;IAAA,KACRC,MAAM;IAAA,KACNC,OAAO;IAAA,KACPO,KAAK;IAQH,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,OAAO,GAAGD,IAAI,CAACC,OAAO;IAC3B,IAAI,CAACV,UAAU,GAAG,CAAC,CAACS,IAAI,CAACT,UAAU;IACnC,IAAI,CAACC,UAAU,GAAG,CAAC,CAACQ,IAAI,CAACR,UAAU;IACnC,IAAI,CAACU,gBAAgB,GAAG,CAAC,CAACF,IAAI,CAACE,gBAAgB;IAC/C,IAAI,CAACT,MAAM,GAAG,CAAC,CAACO,IAAI,CAACP,MAAM;IAC3B,IAAI,CAACC,QAAQ,GAAG,CAAC,CAACM,IAAI,CAACN,QAAQ;IAC/B,IAAI,CAACC,MAAM,GAAG,CAAC,CAACK,IAAI,CAACL,MAAM;IAC3B,IAAI,CAACC,OAAO,GAAG,CAAC,CAACI,IAAI,CAACJ,OAAO;IAC7B,IAAI,CAACO,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,IAAI,GAAGH,IAAI,CAACG,KAAK,GAAG,IAAI;IAChB;MACjC,IAAI,CAACC,aAAa,GAAG,IAAI;IAC3B;EACF;AACF;AAACC,OAAA,CAAAR,iBAAA,GAAAA,iBAAA;AAGM,MAAMS,QAAQ,GAAG,IAAIC,GAAG,CAAoB,CAAC;AAACF,OAAA,CAAAC,QAAA,GAAAA,QAAA;AAErD,SAASE,aAAaA,CAACC,IAAY,EAAEC,OAAqB,GAAG,CAAC,CAAC,EAAa;EAC1EA,OAAO,CAACT,OAAO,GAAGQ,IAAI;EACtB,MAAME,KAAK,GAAGC,WAAW,CAACH,IAAI,EAAEC,OAAO,CAAC;EACxCJ,QAAQ,CAACO,GAAG,CAACJ,IAAI,EAAEE,KAAK,CAAC;EACzB,OAAOA,KAAK;AACd;AAEA,SAASG,WAAWA,CAACL,IAAY,EAAEN,KAAa,EAAE;EAChD,OAAOS,WAAW,CAACH,IAAI,EAAE;IAAElB,UAAU;IAAEY;EAAM,CAAC,CAAC;AACjD;AAEA,IAAIY,gBAAgB,GAAG,CAAC,CAAC;AAClB,MAAMC,UAA+B,GAAG,EAAE;AAACX,OAAA,CAAAW,UAAA,GAAAA,UAAA;AAClD,MAAMC,WAAqB,GAAG,EAAE;AAChC,MAAMC,WAAqB,GAAG,EAAE;AAChC,MAAMC,gBAA2B,GAAG,EAAE;AACtC,MAAMC,gBAA2B,GAAG,EAAE;AACtC,MAAMC,aAAwB,GAAG,EAAE;AAEnC,SAAST,WAAWA,CAACH,IAAY,EAAEC,OAAqB,GAAG,CAAC,CAAC,EAAa;EAAA,IAAAY,cAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,eAAA;EACxE,EAAEV,gBAAgB;EAClBE,WAAW,CAACS,IAAI,CAACjB,IAAI,CAAC;EACtBS,WAAW,CAACQ,IAAI,EAAAJ,cAAA,GAACZ,OAAO,CAACP,KAAK,YAAAmB,cAAA,GAAI,CAAC,CAAC,CAAC;EACrCH,gBAAgB,CAACO,IAAI,EAAAH,mBAAA,GAACb,OAAO,CAACnB,UAAU,YAAAgC,mBAAA,GAAI,KAAK,CAAC;EAClDH,gBAAgB,CAACM,IAAI,EAAAF,mBAAA,GAACd,OAAO,CAAClB,UAAU,YAAAgC,mBAAA,GAAI,KAAK,CAAC;EAClDH,aAAa,CAACK,IAAI,EAAAD,eAAA,GAACf,OAAO,CAACf,MAAM,YAAA8B,eAAA,GAAI,KAAK,CAAC;EAC3CT,UAAU,CAACU,IAAI,CAAC,IAAI7B,iBAAiB,CAACY,IAAI,EAAEC,OAAO,CAAC,CAAC;EAErD,OAAOK,gBAAgB;AACzB;AAEA,SAASY,iBAAiBA,CACxBlB,IAAY,EACZC,OAAqB,GAAG,CAAC,CAAC,EACf;EAAA,IAAAkB,eAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,gBAAA;EACX,EAAEhB,gBAAgB;EAClBT,QAAQ,CAACO,GAAG,CAACJ,IAAI,EAAEM,gBAAgB,CAAC;EACpCE,WAAW,CAACS,IAAI,CAACjB,IAAI,CAAC;EACtBS,WAAW,CAACQ,IAAI,EAAAE,eAAA,GAAClB,OAAO,CAACP,KAAK,YAAAyB,eAAA,GAAI,CAAC,CAAC,CAAC;EACrCT,gBAAgB,CAACO,IAAI,EAAAG,oBAAA,GAACnB,OAAO,CAACnB,UAAU,YAAAsC,oBAAA,GAAI,KAAK,CAAC;EAClDT,gBAAgB,CAACM,IAAI,EAAAI,oBAAA,GAACpB,OAAO,CAAClB,UAAU,YAAAsC,oBAAA,GAAI,KAAK,CAAC;EAClDT,aAAa,CAACK,IAAI,EAAAK,gBAAA,GAACrB,OAAO,CAACf,MAAM,YAAAoC,gBAAA,GAAI,KAAK,CAAC;EAE3Cf,UAAU,CAACU,IAAI,CAAC,IAAI7B,iBAAiB,CAAC,MAAM,EAAEa,OAAO,CAAC,CAAC;EAEvD,OAAOK,gBAAgB;AACzB;AAOO,MAAMiB,EAAE,GAAG;EAEhBC,QAAQ,EAAErB,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACtD0C,YAAY,EAAEtB,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EAC3D2C,WAAW,EAAEvB,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EAC1D4C,QAAQ,EAAExB,WAAW,CAAC,GAAG,CAAC;EAC1ByB,WAAW,EAAEzB,WAAW,CAAC,IAAI,CAAC;EAC9B0B,MAAM,EAAE1B,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACpD+C,SAAS,EAAE3B,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACxDgD,UAAU,EAAE5B,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACzDiD,MAAM,EAAE7B,WAAW,CAAC,GAAG,CAAC;EACxB8B,SAAS,EAAE9B,WAAW,CAAC,IAAI,CAAC;EAC5B+B,MAAM,EAAE/B,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACpDoD,MAAM,EAAEhC,WAAW,CAAC,GAAG,CAAC;EACxBiC,KAAK,EAAEjC,WAAW,CAAC,GAAG,EAAE;IAAErB;EAAW,CAAC,CAAC;EACvCuD,IAAI,EAAElC,WAAW,CAAC,GAAG,EAAE;IAAErB;EAAW,CAAC,CAAC;EACtCwD,KAAK,EAAEnC,WAAW,CAAC,GAAG,EAAE;IAAErB;EAAW,CAAC,CAAC;EACvCyD,WAAW,EAAEpC,WAAW,CAAC,IAAI,EAAE;IAAErB;EAAW,CAAC,CAAC;EAC9C0D,GAAG,EAAErC,WAAW,CAAC,GAAG,CAAC;EACrBsC,QAAQ,EAAEtC,WAAW,CAAC,GAAG,EAAE;IAAErB;EAAW,CAAC,CAAC;EAC1C4D,WAAW,EAAEvC,WAAW,CAAC,IAAI,CAAC;EAC9BwC,KAAK,EAAExC,WAAW,CAAC,IAAI,EAAE;IAAErB;EAAW,CAAC,CAAC;EACxC8D,QAAQ,EAAEzC,WAAW,CAAC,UAAU,CAAC;EACjC0C,QAAQ,EAAE1C,WAAW,CAAC,KAAK,EAAE;IAAErB;EAAW,CAAC,CAAC;EAC5CgE,SAAS,EAAE3C,WAAW,CAAC,GAAG,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAC3CgE,YAAY,EAAE5C,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EAE3DiE,YAAY,EAAE7C,WAAW,CAAC,MAAM,EAAE;IAAEpB;EAAW,CAAC,CAAC;EACjDkE,eAAe,EAAE9C,WAAW,CAAC,OAAO,EAAE;IAAErB,UAAU;IAAEC;EAAW,CAAC,CAAC;EAEjEmE,EAAE,EAAE/C,WAAW,CAAC,GAAG,CAAC;EACpBgD,IAAI,EAAEhD,WAAW,CAAC,GAAG,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAGtCqE,oBAAoB,EAAEjD,WAAW,CAAC,OAAO,CAAC;EAiB1CkD,EAAE,EAAElD,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEG;EAAS,CAAC,CAAC;EAC9CqE,MAAM,EAAEnD,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEG;EAAS,CAAC,CAAC;EACnDsE,WAAW,EAAEpD,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEG;EAAS,CAAC,CAAC;EAIxDuE,SAAS,EAAErD,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEG;EAAS,CAAC,CAAC;EACtDwE,YAAY,EAAEtD,WAAW,CAAC,IAAI,EAAE;IAAErB,UAAU;IAAEG;EAAS,CAAC,CAAC;EAGzDyE,MAAM,EAAEvD,WAAW,CAAC,OAAO,EAAE;IAAEjB,MAAM;IAAEC,OAAO;IAAEJ;EAAW,CAAC,CAAC;EAC7D4E,IAAI,EAAExD,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EAC1D6E,KAAK,EAAEzD,WAAW,CAAC,GAAG,EAAE;IAAErB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EAI3D8E,WAAW,EAAE1D,WAAW,CAAC,IAAI,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAC9C+E,QAAQ,EAAE3D,WAAW,CAAC,IAAI,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAG3CgF,QAAQ,EAAE1D,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;EAC9B2D,iBAAiB,EAAE3D,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;EACvC4D,SAAS,EAAE5D,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;EAC/B6D,UAAU,EAAE7D,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;EAChC8D,SAAS,EAAE9D,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;EAC9B+D,UAAU,EAAE/D,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;EAC/BgE,UAAU,EAAEhE,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;EAC/BiE,QAAQ,EAAEjE,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC;EACzCkE,EAAE,EAAElE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EAC/BmE,EAAE,EAAEnE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EAC/BoE,UAAU,EAAEpE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EACvCqE,QAAQ,EAAErE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EACrCsE,SAAS,EAAEtE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EACtCuE,SAAS,EAAEvE,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC;EACtCwE,OAAO,EAAE1E,WAAW,CAAC,KAAK,EAAE;IAAErB,UAAU;IAAEY,KAAK,EAAE,CAAC;IAAER,MAAM;IAAEH;EAAW,CAAC,CAAC;EAEzE+F,MAAM,EAAE3E,WAAW,CAAC,GAAG,EAAE;IAAET,KAAK,EAAE,EAAE;IAAEX;EAAW,CAAC,CAAC;EAEnDgG,IAAI,EAAE5E,WAAW,CAAC,GAAG,EAAE;IAAET,KAAK,EAAE;EAAG,CAAC,CAAC;EACrCsF,KAAK,EAAE3E,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3B4E,QAAQ,EAAE9E,WAAW,CAAC,IAAI,EAAE;IAC1BrB,UAAU;IACVY,KAAK,EAAE,EAAE;IACTD,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAOFyF,GAAG,EAAEnF,aAAa,CAAC,IAAI,EAAE;IAAEjB,UAAU;IAAEY,KAAK,EAAE;EAAE,CAAC,CAAC;EAClDyF,WAAW,EAAEpF,aAAa,CAAC,YAAY,EAAE;IAAEjB,UAAU;IAAEY,KAAK,EAAE;EAAE,CAAC,CAAC;EAElE0F,MAAM,EAAErF,aAAa,CAAC,OAAO,CAAC;EAC9BsF,KAAK,EAAEtF,aAAa,CAAC,MAAM,EAAE;IAAEjB;EAAW,CAAC,CAAC;EAC5CwG,MAAM,EAAEvF,aAAa,CAAC,OAAO,CAAC;EAC9BwF,SAAS,EAAExF,aAAa,CAAC,UAAU,CAAC;EACpCyF,SAAS,EAAEzF,aAAa,CAAC,UAAU,CAAC;EACpC0F,QAAQ,EAAE1F,aAAa,CAAC,SAAS,EAAE;IAAEjB;EAAW,CAAC,CAAC;EAClD4G,KAAK,EAAE3F,aAAa,CAAC,MAAM,EAAE;IAAEjB;EAAW,CAAC,CAAC;EAC5C6G,QAAQ,EAAE5F,aAAa,CAAC,SAAS,CAAC;EAClC6F,SAAS,EAAE7F,aAAa,CAAC,UAAU,EAAE;IAAEhB;EAAW,CAAC,CAAC;EACpD8G,GAAG,EAAE9F,aAAa,CAAC,IAAI,CAAC;EACxB+F,OAAO,EAAE/F,aAAa,CAAC,QAAQ,EAAE;IAAEjB;EAAW,CAAC,CAAC;EAChDiH,OAAO,EAAEhG,aAAa,CAAC,QAAQ,CAAC;EAChCiG,MAAM,EAAEjG,aAAa,CAAC,OAAO,EAAE;IAAEjB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EAClEkH,IAAI,EAAElG,aAAa,CAAC,KAAK,CAAC;EAC1BmG,IAAI,EAAEnG,aAAa,CAAC,KAAK,CAAC;EAC1BoG,MAAM,EAAEpG,aAAa,CAAC,OAAO,CAAC;EAC9BqG,KAAK,EAAErG,aAAa,CAAC,MAAM,CAAC;EAC5BsG,IAAI,EAAEtG,aAAa,CAAC,KAAK,EAAE;IAAEjB,UAAU;IAAEC;EAAW,CAAC,CAAC;EACtDuH,KAAK,EAAEvG,aAAa,CAAC,MAAM,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC5CwH,MAAM,EAAExG,aAAa,CAAC,OAAO,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC9CyH,MAAM,EAAEzG,aAAa,CAAC,OAAO,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC9C0H,QAAQ,EAAE1G,aAAa,CAAC,SAAS,EAAE;IAAEjB;EAAW,CAAC,CAAC;EAClD4H,OAAO,EAAE3G,aAAa,CAAC,QAAQ,CAAC;EAChC4G,OAAO,EAAE5G,aAAa,CAAC,QAAQ,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAChD6H,KAAK,EAAE7G,aAAa,CAAC,MAAM,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC5C8H,KAAK,EAAE9G,aAAa,CAAC,MAAM,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC5C+H,MAAM,EAAE/G,aAAa,CAAC,OAAO,EAAE;IAAEhB;EAAW,CAAC,CAAC;EAC9CgI,OAAO,EAAEhH,aAAa,CAAC,QAAQ,EAAE;IAAEjB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EACpEiI,KAAK,EAAEjH,aAAa,CAAC,MAAM,EAAE;IAAEjB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EAChEkI,OAAO,EAAElH,aAAa,CAAC,QAAQ,EAAE;IAAEjB,UAAU;IAAEI,MAAM;IAAEH;EAAW,CAAC,CAAC;EAEpEmI,GAAG,EAAEnH,aAAa,CAAC,IAAI,EAAE;IAAEf,MAAM;IAAEF;EAAW,CAAC,CAAC;EAChDqI,IAAI,EAAEpH,aAAa,CAAC,KAAK,EAAE;IAAEf;EAAO,CAAC,CAAC;EACtCoI,MAAM,EAAErH,aAAa,CAAC,OAAO,EAAE;IAAEf;EAAO,CAAC,CAAC;EAM1CqI,GAAG,EAAEnG,iBAAiB,CAAC,IAAI,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC5CuI,OAAO,EAAEpG,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpDwI,MAAM,EAAErG,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClDyI,MAAM,EAAEtG,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClD0I,KAAK,EAAEvG,iBAAiB,CAAC,MAAM,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAChD2I,IAAI,EAAExG,iBAAiB,CAAC,KAAK,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC9C4I,IAAI,EAAEzG,iBAAiB,CAAC,KAAK,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC9C6I,KAAK,EAAE1G,iBAAiB,CAAC,MAAM,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAChD8I,GAAG,EAAE3G,iBAAiB,CAAC,IAAI,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC5C+I,KAAK,EAAE5G,iBAAiB,CAAC,MAAM,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAChDgJ,IAAI,EAAE7G,iBAAiB,CAAC,KAAK,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC9CiJ,OAAO,EAAE9G,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpDkJ,MAAM,EAAE/G,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClDmJ,MAAM,EAAEhH,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAGlDoJ,QAAQ,EAAEjH,iBAAiB,CAAC,SAAS,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACtDqJ,OAAO,EAAElH,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpDsJ,QAAQ,EAAEnH,iBAAiB,CAAC,SAAS,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACtDuJ,OAAO,EAAEpH,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpDwJ,WAAW,EAAErH,iBAAiB,CAAC,YAAY,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC5DyJ,UAAU,EAAEtH,iBAAiB,CAAC,WAAW,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC1D0J,MAAM,EAAEvH,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClD2J,GAAG,EAAExH,iBAAiB,CAAC,IAAI,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC5C4J,OAAO,EAAEzH,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpD6J,MAAM,EAAE1H,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClD8J,QAAQ,EAAE3H,iBAAiB,CAAC,SAAS,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACtD+J,UAAU,EAAE5H,iBAAiB,CAAC,WAAW,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAE1DgK,MAAM,EAAE7H,iBAAiB,CAAC,OAAO,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAClDiK,SAAS,EAAE9H,iBAAiB,CAAC,UAAU,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACxDkK,OAAO,EAAE/H,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAGpDmK,SAAS,EAAEhI,iBAAiB,CAAC,UAAU,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACxDoK,QAAQ,EAAEjI,iBAAiB,CAAC,SAAS,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACtDqK,KAAK,EAAElI,iBAAiB,CAAC,MAAM,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAChDsK,OAAO,EAAEnI,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EACpDuK,UAAU,EAAEpI,iBAAiB,CAAC,WAAW,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAE1DwK,UAAU,EAAErI,iBAAiB,CAAC,WAAW,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAC1DyK,KAAK,EAAEtI,iBAAiB,CAAC,MAAM,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAEhD0K,OAAO,EAAEvI,iBAAiB,CAAC,QAAQ,EAAE;IAAEnC;EAAW,CAAC,CAAC;EAEpDiB,IAAI,EAAEG,WAAW,CAAC,MAAM,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAGzC2K,MAAM,EAAEvJ,WAAW,CAAC,QAAQ,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAC7C4K,GAAG,EAAExJ,WAAW,CAAC,KAAK,EAAE;IAAEpB;EAAW,CAAC,CAAC;EACvC6K,MAAM,EAAEzJ,WAAW,CAAC,QAAQ,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAC7C8K,OAAO,EAAE1J,WAAW,CAAC,SAAS,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAE/C+K,MAAM,EAAE3J,WAAW,CAAC,QAAQ,EAAE;IAAEpB;EAAW,CAAC,CAAC;EAC7CgL,WAAW,EAAE5J,WAAW,CAAC,OAAO,EAAE;IAAEpB;EAAW,CAAC,CAAC;EACjDiL,GAAG,EAAE7J,WAAW,CAAC,KAAK,CAAC;EAGvB8J,OAAO,EAAE9J,WAAW,CAAC,SAAS,CAAC;EAC/B+J,OAAO,EAAE/J,WAAW,CAAC,SAAS,EAAE;IAAErB,UAAU,EAAE;EAAK,CAAC,CAAC;EACrDqL,WAAW,EAAEhK,WAAW,CAAC,aAAa,EAAE;IAAEpB,UAAU,EAAE;EAAK,CAAC,CAAC;EAC7DqL,SAAS,EAAEjK,WAAW,CAAC,WAAW,CAAC;EAGnCkK,WAAW,EAAElK,WAAW,CAAC,IAAI,EAAE;IAAEpB,UAAU,EAAE;EAAK,CAAC;AACrD,CAAU;AAACa,OAAA,CAAA2B,EAAA,GAAAA,EAAA;AAEJ,SAAS+I,iBAAiBA,CAACpK,KAAgB,EAAW;EAC3D,OAAOA,KAAK,MAAU,IAAIA,KAAK,OAAW;AAC5C;AAEO,SAASqK,iCAAiCA,CAACrK,KAAgB,EAAW;EAG3E,OAAOA,KAAK,MAAa;AAC3B;AAEO,SAASsK,0BAA0BA,CAACtK,KAAgB,EAAW;EACpE,OAAOA,KAAK,MAAU,IAAIA,KAAK,OAAW;AAC5C;AAEO,SAASuK,0BAA0BA,CAACvK,KAAgB,EAAW;EACpE,OAAOA,KAAK,MAAU,IAAIA,KAAK,OAAc;AAC/C;AAEO,SAASwK,0BAA0BA,CAACxK,KAAgB,EAAW;EACpE,OAAOQ,gBAAgB,CAACR,KAAK,CAAC;AAChC;AAEO,SAASyK,uBAAuBA,CAACzK,KAAgB,EAAW;EACjE,OAAOS,gBAAgB,CAACT,KAAK,CAAC;AAChC;AAEO,SAAS0K,iBAAiBA,CAAC1K,KAAgB,EAAW;EAC3D,OAAOA,KAAK,MAAS,IAAIA,KAAK,MAAmB;AACnD;AAEO,SAAS2K,kCAAkCA,CAAC3K,KAAgB,EAAW;EAC5E,OAAOA,KAAK,OAAiB,IAAIA,KAAK,OAAc;AACtD;AAEO,SAAS4K,WAAWA,CAAC5K,KAAgB,EAAW;EACrD,OAAOA,KAAK,MAAU,IAAIA,KAAK,MAAa;AAC9C;AAEO,SAAS6K,cAAcA,CAAC7K,KAAgB,EAAW;EACxD,OAAOA,KAAK,MAAU,IAAIA,KAAK,MAAa;AAC9C;AAEO,SAAS8K,eAAeA,CAAC9K,KAAgB,EAAW;EACzD,OAAOA,KAAK,MAAe,IAAIA,KAAK,MAAkB;AACxD;AAEO,SAAS+K,cAAcA,CAAC/K,KAAgB,EAAW;EACxD,OAAOA,KAAK,OAAc;AAC5B;AAEO,SAASgL,aAAaA,CAAChL,KAAgB,EAAW;EACvD,OAAOU,aAAa,CAACV,KAAK,CAAC;AAC7B;AAEO,SAASiL,qBAAqBA,CAACjL,KAAgB,EAAW;EAC/D,OAAOA,KAAK,OAAa,IAAIA,KAAK,OAAc;AAClD;AAEO,SAASkL,yBAAyBA,CAAClL,KAAgB,EAAW;EACnE,OAAOA,KAAK,OAAgB,IAAIA,KAAK,OAAY;AACnD;AAEO,SAASmL,cAAcA,CAACnL,KAAgB,EAAU;EACvD,OAAOM,WAAW,CAACN,KAAK,CAAC;AAC3B;AAEO,SAASoL,uBAAuBA,CAACpL,KAAgB,EAAU;EAChE,OAAOO,WAAW,CAACP,KAAK,CAAC;AAC3B;AAEO,SAASqL,qBAAqBA,CAACrL,KAAgB,EAAW;EAC/D,OAAOO,WAAW,CAACP,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AAEO,SAASsL,uBAAuBA,CAACtL,KAAgB,EAAW;EACjE,OAAOA,KAAK,OAAgB;AAC9B;AAEO,SAASuL,eAAeA,CAACvL,KAAgB,EAAW;EACzD,OAAOA,KAAK,MAAmB,IAAIA,KAAK,MAAsB;AAChE;AAEO,SAASwL,gBAAgBA,CAACxL,KAAgB,EAAqB;EACpE,OAAOK,UAAU,CAACL,KAAK,CAAC;AAC1B;AAEO,SAASyL,WAAWA,CAACC,GAAQ,EAAW;EAC7C,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAChC;AAEmC;EACjCrL,UAAU,GAAW,CAACZ,aAAa,GAAGkM,OAAO,IAAI;IAC/CA,OAAO,CAACC,GAAG,CAAC,CAAC;EACf,CAAC;EAEDvL,UAAU,GAAW,CAACZ,aAAa,GACjCY,UAAU,GAAe,CAACZ,aAAa,GACvCY,UAAU,IAAiB,CAACZ,aAAa,GACvCkM,OAAO,IAAI;IACTA,OAAO,CAAC5K,IAAI,CAAC8K,cAAE,CAACC,KAAK,CAAC;EACxB,CAAC;EAELzL,UAAU,IAAc,CAACZ,aAAa,GAAGkM,OAAO,IAAI;IAClD,IAAIA,OAAO,CAACA,OAAO,CAACI,MAAM,GAAG,CAAC,CAAC,KAAKF,cAAE,CAACnJ,QAAQ,EAAE;MAC/CiJ,OAAO,CAACC,GAAG,CAAC,CAAC;IACf,CAAC,MAAM;MACLD,OAAO,CAAC5K,IAAI,CAAC8K,cAAE,CAACnJ,QAAQ,CAAC;IAC3B;EACF,CAAC;EAEDrC,UAAU,KAAgB,CAACZ,aAAa,GAAGkM,OAAO,IAAI;IACpDA,OAAO,CAAC5K,IAAI,CAAC8K,cAAE,CAACG,MAAM,EAAEH,cAAE,CAACI,MAAM,CAAC;EACpC,CAAC;AACH"}