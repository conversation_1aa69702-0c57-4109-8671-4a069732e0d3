<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能占星助手 - AI Astrology Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            min-height: 100vh;
        }

        .chat-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.2em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            color: #718096;
            font-size: 1.1em;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 500px;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #f8fafc;
            border-radius: 10px;
            margin-bottom: 20px;
            max-height: 400px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: #e2e8f0;
            color: #2d3748;
            margin-right: auto;
        }

        .input-area {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-container {
            flex: 1;
            position: relative;
        }

        #userInput {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 120px;
            transition: border-color 0.3s;
        }

        #userInput:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .quick-questions {
            margin-bottom: 20px;
        }

        .quick-questions h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .quick-btn {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            padding: 10px 15px;
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: left;
            font-size: 14px;
            color: #4a5568;
        }

        .quick-btn:hover {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }

        .status-panel {
            background: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-panel h3 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }

        .status-online {
            background: #48bb78;
        }

        .status-offline {
            background: #f56565;
        }

        .chart-display {
            background: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .chart-placeholder {
            color: #a0aec0;
            font-size: 14px;
        }

        .chart-image {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tools-used {
            margin-top: 10px;
            padding: 8px 12px;
            background: #edf2f7;
            border-radius: 6px;
            font-size: 12px;
            color: #4a5568;
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .sidebar {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主聊天區域 -->
        <div class="chat-section">
            <div class="header">
                <h1>🌟 智能占星助手</h1>
                <p>結合AI技術與占星學智慧，為您提供專業的占星諮詢服務</p>
            </div>

            <div class="chat-container">
                <div class="messages" id="messages">
                    <div class="message ai-message">
                        <div>歡迎使用智能占星助手！我可以幫您：</div>
                        <div style="margin-top: 10px;">
                            🔮 解答占星學理論問題<br>
                            📊 分析個人出生星盤<br>
                            🌙 提供天象資訊查詢<br>
                            ⭐ 給出個人化建議
                        </div>
                        <div style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                            請輸入您的問題，或點擊右側的快速問題開始對話！
                        </div>
                    </div>
                </div>

                <div class="input-area">
                    <div class="input-container">
                        <textarea 
                            id="userInput" 
                            placeholder="請輸入您的占星問題..."
                            rows="2"
                        ></textarea>
                    </div>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        ➤
                    </button>
                </div>
            </div>
        </div>

        <!-- 側邊欄 -->
        <div class="sidebar">
            <!-- 系統狀態 -->
            <div class="status-panel">
                <h3>🔧 系統狀態</h3>
                <div class="status-item">
                    <span><span class="status-indicator status-online"></span>API服務</span>
                    <span id="apiStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span><span class="status-indicator status-offline"></span>Agent</span>
                    <span id="agentStatus">檢查中...</span>
                </div>
                <div class="status-item">
                    <span><span class="status-indicator status-offline"></span>RAG檢索</span>
                    <span id="ragStatus">檢查中...</span>
                </div>
            </div>

            <!-- 快速問題 -->
            <div class="quick-questions">
                <h3>💫 快速問題</h3>
                <button class="quick-btn" onclick="askQuestion('什麼是上升星座？')">
                    什麼是上升星座？
                </button>
                <button class="quick-btn" onclick="askQuestion('請分析我的出生星盤：1992年12月18日下午3點，台北')">
                    分析我的出生星盤
                </button>
                <button class="quick-btn" onclick="askQuestion('2024年水星逆行時間')">
                    2024年水星逆行時間
                </button>
                <button class="quick-btn" onclick="askQuestion('金星在第七宮代表什麼？')">
                    金星在第七宮的意義
                </button>
                <button class="quick-btn" onclick="askQuestion('如何看懂星盤中的相位？')">
                    如何看懂星盤相位
                </button>
            </div>

            <!-- 星圖顯示區域 -->
            <div class="chart-display" id="chartDisplay">
                <div class="chart-placeholder">
                    🌌 星圖顯示區域<br>
                    <small>當生成星盤時，圖表將在此顯示</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API基礎URL
        const API_BASE = 'http://localhost:8000';
        
        // 全局變量
        let isLoading = false;

        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            setupEventListeners();
        });

        // 設置事件監聽器
        function setupEventListeners() {
            const userInput = document.getElementById('userInput');
            const sendBtn = document.getElementById('sendBtn');

            // Enter鍵發送消息（Shift+Enter換行）
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 自動調整輸入框高度
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        // 檢查系統狀態
        async function checkSystemStatus() {
            try {
                // 檢查API狀態
                const healthResponse = await fetch(`${API_BASE}/health`);
                if (healthResponse.ok) {
                    updateStatus('apiStatus', '正常', 'status-online');
                } else {
                    updateStatus('apiStatus', '異常', 'status-offline');
                }

                // 檢查Agent狀態
                const agentResponse = await fetch(`${API_BASE}/agent/status`);
                if (agentResponse.ok) {
                    const agentData = await agentResponse.json();
                    const status = agentData.status === 'ready' ? '就緒' : '初始化中';
                    const statusClass = agentData.status === 'ready' ? 'status-online' : 'status-offline';
                    updateStatus('agentStatus', status, statusClass);
                    
                    // 更新RAG狀態
                    const ragAvailable = agentData.agent_info?.rag_tools_count > 0;
                    updateStatus('ragStatus', ragAvailable ? '可用' : '不可用', 
                               ragAvailable ? 'status-online' : 'status-offline');
                } else {
                    updateStatus('agentStatus', '離線', 'status-offline');
                    updateStatus('ragStatus', '不可用', 'status-offline');
                }
            } catch (error) {
                console.error('狀態檢查失敗:', error);
                updateStatus('apiStatus', '連接失敗', 'status-offline');
                updateStatus('agentStatus', '連接失敗', 'status-offline');
                updateStatus('ragStatus', '連接失敗', 'status-offline');
            }
        }

        // 更新狀態顯示
        function updateStatus(elementId, text, statusClass) {
            const element = document.getElementById(elementId);
            const indicator = element.parentElement.querySelector('.status-indicator');
            
            element.textContent = text;
            indicator.className = `status-indicator ${statusClass}`;
        }

        // 快速問題
        function askQuestion(question) {
            document.getElementById('userInput').value = question;
            sendMessage();
        }

        // 發送消息
        async function sendMessage() {
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();
            
            if (!message || isLoading) return;

            // 添加用戶消息
            addMessage(message, 'user');
            userInput.value = '';
            userInput.style.height = 'auto';

            // 設置載入狀態
            setLoading(true);
            
            // 添加載入消息
            const loadingId = addMessage('正在思考中...', 'ai', true);

            try {
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: message,
                        include_rag: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // 移除載入消息
                removeMessage(loadingId);
                
                // 添加AI回應
                addMessage(data.response, 'ai', false, data.tools_used);
                
                // 如果有星圖相關內容，嘗試顯示
                if (data.response.includes('星盤') || data.response.includes('星圖')) {
                    updateChartDisplay('星圖生成中...', true);
                }

            } catch (error) {
                console.error('發送消息失敗:', error);
                removeMessage(loadingId);
                addMessage(`抱歉，發生錯誤：${error.message}`, 'ai');
            } finally {
                setLoading(false);
            }
        }

        // 添加消息到聊天區域
        function addMessage(content, type, isLoading = false, toolsUsed = []) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            const messageId = 'msg_' + Date.now();
            messageDiv.id = messageId;
            messageDiv.className = `message ${type}-message`;
            
            let messageContent = content;
            if (isLoading) {
                messageContent = `<span class="loading"></span> ${content}`;
            }
            
            messageDiv.innerHTML = messageContent;
            
            // 添加工具使用信息
            if (toolsUsed && toolsUsed.length > 0) {
                const toolsDiv = document.createElement('div');
                toolsDiv.className = 'tools-used';
                toolsDiv.textContent = `🔧 使用工具: ${toolsUsed.join(', ')}`;
                messageDiv.appendChild(toolsDiv);
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
            
            return messageId;
        }

        // 移除消息
        function removeMessage(messageId) {
            const message = document.getElementById(messageId);
            if (message) {
                message.remove();
            }
        }

        // 設置載入狀態
        function setLoading(loading) {
            isLoading = loading;
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = loading;
            sendBtn.textContent = loading ? '⏳' : '➤';
        }

        // 更新星圖顯示
        function updateChartDisplay(content, isLoading = false) {
            const chartDisplay = document.getElementById('chartDisplay');
            
            if (isLoading) {
                chartDisplay.innerHTML = `
                    <div class="loading"></div>
                    <div style="margin-top: 10px;">${content}</div>
                `;
            } else {
                chartDisplay.innerHTML = `
                    <div class="chart-placeholder">
                        ${content}
                    </div>
                `;
            }
        }

        // 定期檢查系統狀態
        setInterval(checkSystemStatus, 30000); // 每30秒檢查一次
    </script>
</body>
</html>
